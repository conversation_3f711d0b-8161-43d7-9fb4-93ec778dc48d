from flask import Flask, render_template, jsonify, request, redirect, url_for
from database import create_app, init_database
from models import db, Equipment, Intervention, SparePart, Production, Stock, Employee, TimeRecord, Leave
from datetime import datetime, date, timedelta
from sqlalchemy import func, desc
import json

app = create_app()

# Routes principales
@app.route('/')
def dashboard():
    """Page d'accueil avec tableau de bord"""
    return render_template('dashboard.html')

@app.route('/api/dashboard/kpis')
def get_dashboard_kpis():
    """API pour récupérer les KPIs du tableau de bord"""
    try:
        # Calcul du taux de panne (équipements en panne / total)
        total_equipment = Equipment.query.count()
        broken_equipment = Equipment.query.filter_by(status='En panne').count()
        breakdown_rate = (broken_equipment / total_equipment * 100) if total_equipment > 0 else 0
        
        # Production journalière (aujourd'hui)
        today = date.today()
        daily_production = db.session.query(func.sum(Production.quantity_produced)).filter(
            Production.date == today
        ).scalar() or 0
        
        # Taux d'absentéisme (aujourd'hui)
        total_employees = Employee.query.filter_by(status='Actif').count()
        absent_today = TimeRecord.query.filter(
            TimeRecord.date == today,
            TimeRecord.status == 'Absent'
        ).count()
        absenteeism_rate = (absent_today / total_employees * 100) if total_employees > 0 else 0
        
        # Niveau de stock moyen
        stocks = Stock.query.all()
        if stocks:
            avg_stock_level = sum(
                (stock.quantity_current / stock.quantity_max * 100) 
                for stock in stocks if stock.quantity_max > 0
            ) / len(stocks)
        else:
            avg_stock_level = 0
        
        return jsonify({
            'breakdown_rate': round(breakdown_rate, 1),
            'daily_production': int(daily_production),
            'absenteeism_rate': round(absenteeism_rate, 1),
            'avg_stock_level': round(avg_stock_level, 0)
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/dashboard/performance')
def get_performance_data():
    """API pour les données de performance des modules"""
    try:
        # Performance Maintenance (basée sur les interventions terminées à temps)
        total_interventions = Intervention.query.count()
        completed_interventions = Intervention.query.filter_by(status='Terminé').count()
        maintenance_performance = (completed_interventions / total_interventions * 100) if total_interventions > 0 else 0
        
        # Performance Production (basée sur l'atteinte des objectifs)
        last_week = date.today() - timedelta(days=7)
        productions = Production.query.filter(Production.date >= last_week).all()
        if productions:
            production_performance = sum(
                min(100, (prod.quantity_produced / prod.quantity_target * 100))
                for prod in productions
            ) / len(productions)
        else:
            production_performance = 0
        
        # Performance Personnel (basée sur la présence)
        personnel_performance = 100 - (absenteeism_rate if 'absenteeism_rate' in locals() else 5)
        
        return jsonify({
            'maintenance': round(maintenance_performance, 0),
            'production': round(production_performance, 0),
            'personnel': round(personnel_performance, 0)
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/dashboard/alerts')
def get_alerts():
    """API pour récupérer les alertes récentes"""
    try:
        alerts = []
        
        # Alertes stock critique
        critical_stocks = Stock.query.filter(Stock.quantity_current <= Stock.quantity_min).all()
        for stock in critical_stocks:
            alerts.append({
                'type': 'critical',
                'message': f'Stock {stock.product_name} critique ({stock.quantity_current} {stock.unit})',
                'icon': 'fas fa-exclamation-circle',
                'color': '#e74c3c'
            })
        
        # Alertes maintenance en retard
        overdue_maintenance = Equipment.query.filter(
            Equipment.next_maintenance < date.today()
        ).all()
        for equipment in overdue_maintenance:
            alerts.append({
                'type': 'warning',
                'message': f'Maintenance {equipment.name} en retard',
                'icon': 'fas fa-exclamation-circle',
                'color': '#f39c12'
            })
        
        # Alertes production
        yesterday = date.today() - timedelta(days=1)
        low_production = Production.query.filter(
            Production.date == yesterday,
            Production.quantity_produced < (Production.quantity_target * 0.85)
        ).all()
        for prod in low_production:
            alerts.append({
                'type': 'info',
                'message': f'Production {prod.line} en baisse de {round((1 - prod.quantity_produced/prod.quantity_target) * 100)}%',
                'icon': 'fas fa-exclamation-circle',
                'color': '#3498db'
            })
        
        # Limiter à 5 alertes
        return jsonify(alerts[:5])
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Routes pour les modules
@app.route('/maintenance')
def maintenance():
    """Page du module maintenance"""
    return render_template('maintenance.html')

@app.route('/production')
def production():
    """Page du module production"""
    return render_template('production.html')

@app.route('/personnel')
def personnel():
    """Page du module personnel"""
    return render_template('personnel.html')

# APIs pour le module Maintenance
@app.route('/api/maintenance/equipments')
def get_equipments():
    """API pour récupérer la liste des équipements"""
    try:
        equipments = Equipment.query.all()
        return jsonify([{
            'id': eq.id,
            'name': eq.name,
            'code': eq.code,
            'type': eq.type,
            'location': eq.location,
            'status': eq.status,
            'installation_date': eq.installation_date.isoformat() if eq.installation_date else None,
            'last_maintenance': eq.last_maintenance.isoformat() if eq.last_maintenance else None,
            'next_maintenance': eq.next_maintenance.isoformat() if eq.next_maintenance else None
        } for eq in equipments])
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/maintenance/interventions')
def get_interventions():
    """API pour récupérer les interventions"""
    try:
        interventions = Intervention.query.order_by(desc(Intervention.created_at)).limit(20).all()
        return jsonify([{
            'id': inter.id,
            'equipment_name': inter.equipment.name,
            'type': inter.type,
            'description': inter.description,
            'start_time': inter.start_time.isoformat() if inter.start_time else None,
            'end_time': inter.end_time.isoformat() if inter.end_time else None,
            'technician': inter.technician,
            'cost': inter.cost,
            'status': inter.status
        } for inter in interventions])
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/maintenance/spare-parts')
def get_spare_parts():
    """API pour récupérer les pièces de rechange"""
    try:
        spare_parts = SparePart.query.all()
        return jsonify([{
            'id': part.id,
            'equipment_name': part.equipment.name,
            'name': part.name,
            'reference': part.reference,
            'quantity_stock': part.quantity_stock,
            'quantity_min': part.quantity_min,
            'unit_price': part.unit_price,
            'supplier': part.supplier,
            'status': 'Critique' if part.quantity_stock <= part.quantity_min else 'Normal'
        } for part in spare_parts])
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# APIs pour le module Production
@app.route('/api/production/daily')
def get_daily_production():
    """API pour récupérer la production journalière"""
    try:
        last_7_days = date.today() - timedelta(days=7)
        productions = Production.query.filter(Production.date >= last_7_days).order_by(desc(Production.date)).all()
        return jsonify([{
            'id': prod.id,
            'date': prod.date.isoformat(),
            'line': prod.line,
            'shift': prod.shift,
            'product_type': prod.product_type,
            'quantity_produced': prod.quantity_produced,
            'quantity_target': prod.quantity_target,
            'efficiency': round((prod.quantity_produced / prod.quantity_target * 100), 1),
            'quality_rate': round(prod.quality_rate, 1),
            'downtime_minutes': prod.downtime_minutes
        } for prod in productions])
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/production/stocks')
def get_stocks():
    """API pour récupérer les niveaux de stock"""
    try:
        stocks = Stock.query.all()
        return jsonify([{
            'id': stock.id,
            'product_name': stock.product_name,
            'product_code': stock.product_code,
            'category': stock.category,
            'quantity_current': stock.quantity_current,
            'quantity_min': stock.quantity_min,
            'quantity_max': stock.quantity_max,
            'unit': stock.unit,
            'location': stock.location,
            'fill_rate': round((stock.quantity_current / stock.quantity_max * 100), 1),
            'status': 'Critique' if stock.quantity_current <= stock.quantity_min else 
                     'Bas' if stock.quantity_current <= stock.quantity_min * 2 else 'Normal'
        } for stock in stocks])
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# APIs pour le module Personnel
@app.route('/api/personnel/employees')
def get_employees():
    """API pour récupérer la liste des employés"""
    try:
        employees = Employee.query.filter_by(status='Actif').all()
        return jsonify([{
            'id': emp.id,
            'employee_id': emp.employee_id,
            'first_name': emp.first_name,
            'last_name': emp.last_name,
            'position': emp.position,
            'department': emp.department,
            'hire_date': emp.hire_date.isoformat(),
            'salary': emp.salary,
            'status': emp.status
        } for emp in employees])
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/personnel/attendance')
def get_attendance():
    """API pour récupérer les données de pointage"""
    try:
        last_7_days = date.today() - timedelta(days=7)
        records = TimeRecord.query.filter(TimeRecord.date >= last_7_days).order_by(desc(TimeRecord.date)).all()
        return jsonify([{
            'id': record.id,
            'employee_name': f"{record.employee.first_name} {record.employee.last_name}",
            'date': record.date.isoformat(),
            'check_in': record.check_in.strftime('%H:%M') if record.check_in else None,
            'check_out': record.check_out.strftime('%H:%M') if record.check_out else None,
            'break_duration': record.break_duration,
            'overtime_hours': record.overtime_hours,
            'status': record.status
        } for record in records])
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/personnel/leaves')
def get_leaves():
    """API pour récupérer les congés"""
    try:
        leaves = Leave.query.order_by(desc(Leave.created_at)).limit(20).all()
        return jsonify([{
            'id': leave.id,
            'employee_name': f"{leave.employee.first_name} {leave.employee.last_name}",
            'type': leave.type,
            'start_date': leave.start_date.isoformat(),
            'end_date': leave.end_date.isoformat(),
            'days_count': leave.days_count,
            'status': leave.status,
            'reason': leave.reason
        } for leave in leaves])
    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    init_database(app)
    app.run(debug=True, host='0.0.0.0', port=5000)
