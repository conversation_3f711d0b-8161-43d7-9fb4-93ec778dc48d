"""
Application FastAPI pour la Gestion Industrielle
"""

from fastapi import FastAPI, Depends, HTTPException, Request
from fastapi.responses import HTMLResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from sqlalchemy.orm import Session
from sqlalchemy import func, desc
from typing import List
from datetime import date, datetime, timedelta
import uvicorn

# Imports locaux
from fastapi_database import get_db, init_database
from fastapi_models import *
from schemas import *

# Créer l'application FastAPI
app = FastAPI(
    title="Application de Gestion Industrielle",
    description="API complète pour la gestion industrielle avec maintenance, production et personnel",
    version="2.0.0",
    docs_url="/api/docs",  # Documentation Swagger
    redoc_url="/api/redoc"  # Documentation ReDoc
)

# Configuration des fichiers statiques et templates
app.mount("/static", StaticFiles(directory="static"), name="static")
templates = Jinja2Templates(directory="templates")

# Initialiser la base de données au démarrage
@app.on_event("startup")
async def startup_event():
    init_database()

# Routes pour les pages HTML
@app.get("/", response_class=HTMLResponse)
async def dashboard(request: Request):
    """Page d'accueil avec tableau de bord"""
    return templates.TemplateResponse("fastapi_dashboard.html", {"request": request})

@app.get("/maintenance", response_class=HTMLResponse)
async def maintenance(request: Request):
    """Page du module maintenance"""
    return templates.TemplateResponse("fastapi_maintenance.html", {"request": request})

@app.get("/production", response_class=HTMLResponse)
async def production(request: Request):
    """Page du module production"""
    return templates.TemplateResponse("fastapi_production.html", {"request": request})

@app.get("/personnel", response_class=HTMLResponse)
async def personnel(request: Request):
    """Page du module personnel"""
    return templates.TemplateResponse("fastapi_personnel.html", {"request": request})

@app.get("/help", response_class=HTMLResponse)
async def help_page(request: Request):
    """Page d'aide et documentation"""
    return templates.TemplateResponse("fastapi_help.html", {"request": request})

# APIs Dashboard
@app.get("/api/dashboard/kpis", response_model=DashboardKPIs)
async def get_dashboard_kpis(db: Session = Depends(get_db)):
    """Récupérer les KPIs du tableau de bord"""
    try:
        # Calcul du taux de panne
        total_equipment = db.query(Equipment).count()
        broken_equipment = db.query(Equipment).filter(Equipment.status == 'En panne').count()
        breakdown_rate = (broken_equipment / total_equipment * 100) if total_equipment > 0 else 0
        
        # Production journalière
        today = date.today()
        daily_production_result = db.query(func.sum(Production.quantity_produced)).filter(
            Production.date == today
        ).scalar()
        daily_production = daily_production_result if daily_production_result is not None else 0
        
        # Taux d'absentéisme
        total_employees = db.query(Employee).filter(Employee.status == 'Actif').count()
        absent_today = db.query(TimeRecord).filter(
            TimeRecord.date == today,
            TimeRecord.status == 'Absent'
        ).count()
        absenteeism_rate = (absent_today / total_employees * 100) if total_employees > 0 else 0
        
        # Niveau de stock moyen
        stocks = db.query(Stock).all()
        if stocks:
            avg_stock_level = sum(
                (stock.quantity_current / stock.quantity_max * 100) 
                for stock in stocks if stock.quantity_max > 0
            ) / len(stocks)
        else:
            avg_stock_level = 0
        
        return DashboardKPIs(
            breakdown_rate=round(breakdown_rate, 1),
            daily_production=int(daily_production),
            absenteeism_rate=round(absenteeism_rate, 1),
            avg_stock_level=round(avg_stock_level, 0)
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/dashboard/performance", response_model=PerformanceData)
async def get_performance_data(db: Session = Depends(get_db)):
    """Récupérer les données de performance des modules"""
    try:
        # Performance Maintenance
        total_interventions = db.query(Intervention).count()
        completed_interventions = db.query(Intervention).filter(Intervention.status == 'Terminé').count()
        maintenance_performance = (completed_interventions / total_interventions * 100) if total_interventions > 0 else 0
        
        # Performance Production
        last_week = date.today() - timedelta(days=7)
        productions = db.query(Production).filter(Production.date >= last_week).all()
        if productions:
            production_performance = sum(
                min(100, (prod.quantity_produced / prod.quantity_target * 100))
                for prod in productions
            ) / len(productions)
        else:
            production_performance = 0
        
        # Performance Personnel
        personnel_performance = 100 - (5)  # Valeur par défaut
        
        return PerformanceData(
            maintenance=round(maintenance_performance, 0),
            production=round(production_performance, 0),
            personnel=round(personnel_performance, 0)
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/dashboard/alerts", response_model=List[Alert])
async def get_alerts(db: Session = Depends(get_db)):
    """Récupérer les alertes récentes"""
    try:
        alerts = []
        
        # Alertes stock critique
        critical_stocks = db.query(Stock).filter(Stock.quantity_current <= Stock.quantity_min).all()
        for stock in critical_stocks:
            alerts.append(Alert(
                type="critical",
                message=f"Stock {stock.product_name} critique ({stock.quantity_current} {stock.unit})",
                icon="fas fa-exclamation-circle",
                color="#e74c3c"
            ))
        
        # Alertes maintenance en retard
        overdue_maintenance = db.query(Equipment).filter(
            Equipment.next_maintenance < date.today()
        ).all()
        for equipment in overdue_maintenance:
            alerts.append(Alert(
                type="warning",
                message=f"Maintenance {equipment.name} en retard",
                icon="fas fa-exclamation-circle",
                color="#f39c12"
            ))
        
        # Alertes production
        yesterday = date.today() - timedelta(days=1)
        low_production = db.query(Production).filter(
            Production.date == yesterday,
            Production.quantity_produced < (Production.quantity_target * 0.85)
        ).all()
        for prod in low_production:
            alerts.append(Alert(
                type="info",
                message=f"Production {prod.line} en baisse de {round((1 - prod.quantity_produced/prod.quantity_target) * 100)}%",
                icon="fas fa-exclamation-circle",
                color="#3498db"
            ))
        
        return alerts[:5]  # Limiter à 5 alertes
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/dashboard/production-trend", response_model=ProductionTrend)
async def get_production_trend(db: Session = Depends(get_db)):
    """Récupérer la tendance de production des 7 derniers jours"""
    try:
        last_7_days = date.today() - timedelta(days=7)
        
        daily_production = db.query(
            Production.date,
            func.sum(Production.quantity_produced).label('total_produced'),
            func.sum(Production.quantity_target).label('total_target')
        ).filter(
            Production.date >= last_7_days
        ).group_by(Production.date).order_by(Production.date).all()
        
        labels = []
        produced_data = []
        target_data = []
        
        for record in daily_production:
            labels.append(record.date.strftime('%d/%m'))
            produced_data.append(int(record.total_produced))
            target_data.append(int(record.total_target))
        
        return ProductionTrend(
            labels=labels,
            datasets=[
                ProductionTrendDataset(
                    label="Production Réalisée",
                    data=produced_data,
                    borderColor="#2ecc71",
                    backgroundColor="rgba(46, 204, 113, 0.1)",
                    tension=0.4
                ),
                ProductionTrendDataset(
                    label="Objectif",
                    data=target_data,
                    borderColor="#3498db",
                    backgroundColor="rgba(52, 152, 219, 0.1)",
                    tension=0.4
                )
            ]
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# APIs Maintenance - Équipements
@app.get("/api/maintenance/equipments", response_model=List[Equipment])
async def get_equipments(db: Session = Depends(get_db)):
    """Récupérer la liste des équipements"""
    return db.query(Equipment).all()

@app.post("/api/maintenance/equipments", response_model=Equipment)
async def create_equipment(equipment: EquipmentCreate, db: Session = Depends(get_db)):
    """Créer un nouvel équipement"""
    try:
        # Vérifier si le code existe déjà
        existing = db.query(Equipment).filter(Equipment.code == equipment.code).first()
        if existing:
            raise HTTPException(status_code=400, detail="Un équipement avec ce code existe déjà")

        db_equipment = Equipment(**equipment.model_dump())
        db.add(db_equipment)
        db.commit()
        db.refresh(db_equipment)
        return db_equipment
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/maintenance/equipments/{equipment_id}", response_model=Equipment)
async def get_equipment(equipment_id: int, db: Session = Depends(get_db)):
    """Récupérer un équipement par ID"""
    equipment = db.query(Equipment).filter(Equipment.id == equipment_id).first()
    if not equipment:
        raise HTTPException(status_code=404, detail="Équipement non trouvé")
    return equipment

@app.put("/api/maintenance/equipments/{equipment_id}", response_model=Equipment)
async def update_equipment(equipment_id: int, equipment_update: EquipmentUpdate, db: Session = Depends(get_db)):
    """Modifier un équipement"""
    try:
        db_equipment = db.query(Equipment).filter(Equipment.id == equipment_id).first()
        if not db_equipment:
            raise HTTPException(status_code=404, detail="Équipement non trouvé")

        # Vérifier le code unique si modifié
        if equipment_update.code and equipment_update.code != db_equipment.code:
            existing = db.query(Equipment).filter(Equipment.code == equipment_update.code).first()
            if existing:
                raise HTTPException(status_code=400, detail="Un équipement avec ce code existe déjà")

        # Mettre à jour les champs modifiés
        update_data = equipment_update.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_equipment, field, value)

        db.commit()
        db.refresh(db_equipment)
        return db_equipment
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@app.delete("/api/maintenance/equipments/{equipment_id}")
async def delete_equipment(equipment_id: int, db: Session = Depends(get_db)):
    """Supprimer un équipement"""
    try:
        db_equipment = db.query(Equipment).filter(Equipment.id == equipment_id).first()
        if not db_equipment:
            raise HTTPException(status_code=404, detail="Équipement non trouvé")

        db.delete(db_equipment)
        db.commit()
        return {"message": "Équipement supprimé avec succès"}
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

# APIs Maintenance - Interventions
@app.get("/api/maintenance/interventions", response_model=List[Intervention])
async def get_interventions(db: Session = Depends(get_db)):
    """Récupérer les interventions"""
    return db.query(Intervention).order_by(desc(Intervention.created_at)).limit(20).all()

@app.post("/api/maintenance/interventions", response_model=Intervention)
async def create_intervention(intervention: InterventionCreate, db: Session = Depends(get_db)):
    """Créer une nouvelle intervention"""
    try:
        # Vérifier que l'équipement existe
        equipment = db.query(Equipment).filter(Equipment.id == intervention.equipment_id).first()
        if not equipment:
            raise HTTPException(status_code=404, detail="Équipement non trouvé")

        db_intervention = Intervention(**intervention.model_dump())
        db.add(db_intervention)
        db.commit()
        db.refresh(db_intervention)
        return db_intervention
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

# APIs Maintenance - Pièces de rechange
@app.get("/api/maintenance/spare-parts", response_model=List[SparePart])
async def get_spare_parts(db: Session = Depends(get_db)):
    """Récupérer les pièces de rechange"""
    return db.query(SparePart).all()

# APIs Production
@app.get("/api/production/daily", response_model=List[Production])
async def get_daily_production(db: Session = Depends(get_db)):
    """Récupérer la production journalière"""
    last_7_days = date.today() - timedelta(days=7)
    return db.query(Production).filter(Production.date >= last_7_days).order_by(desc(Production.date)).all()

@app.get("/api/production/stocks", response_model=List[Stock])
async def get_stocks(db: Session = Depends(get_db)):
    """Récupérer les niveaux de stock"""
    return db.query(Stock).all()

# APIs Personnel
@app.get("/api/personnel/employees", response_model=List[Employee])
async def get_employees(db: Session = Depends(get_db)):
    """Récupérer la liste des employés"""
    return db.query(Employee).filter(Employee.status == 'Actif').all()

@app.post("/api/personnel/employees", response_model=Employee)
async def create_employee(employee: EmployeeCreate, db: Session = Depends(get_db)):
    """Créer un nouvel employé"""
    try:
        # Vérifier si l'ID employé existe déjà
        existing = db.query(Employee).filter(Employee.employee_id == employee.employee_id).first()
        if existing:
            raise HTTPException(status_code=400, detail="Un employé avec cet ID existe déjà")

        db_employee = Employee(**employee.model_dump())
        db.add(db_employee)
        db.commit()
        db.refresh(db_employee)
        return db_employee
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/personnel/attendance", response_model=List[TimeRecord])
async def get_attendance(db: Session = Depends(get_db)):
    """Récupérer les données de pointage"""
    last_7_days = date.today() - timedelta(days=7)
    return db.query(TimeRecord).filter(TimeRecord.date >= last_7_days).order_by(desc(TimeRecord.date)).all()

@app.get("/api/personnel/leaves", response_model=List[Leave])
async def get_leaves(db: Session = Depends(get_db)):
    """Récupérer les congés"""
    return db.query(Leave).order_by(desc(Leave.created_at)).limit(20).all()

if __name__ == "__main__":
    uvicorn.run(
        "fastapi_app:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
