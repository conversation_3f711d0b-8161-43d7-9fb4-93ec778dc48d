@echo off
echo.
echo ================================================
echo   Application de Gestion Industrielle - FastAPI
echo ================================================
echo.

REM Vérifier si Python est installé
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python n'est pas installé ou n'est pas dans le PATH
    echo Veuillez installer Python 3.8 ou supérieur depuis https://python.org
    pause
    exit /b 1
)

echo ✅ Python détecté
echo.

REM Installer les dépendances FastAPI si nécessaire
echo 📦 Vérification des dépendances FastAPI...
python -c "import fastapi, uvicorn, sqlalchemy, pydantic" >nul 2>&1
if errorlevel 1 (
    echo Installation des dépendances FastAPI...
    python -m pip install -r requirements.txt
    if errorlevel 1 (
        echo ❌ Erreur lors de l'installation des dépendances
        pause
        exit /b 1
    )
)

echo ✅ Dépendances FastAPI OK
echo.

REM Vérifier la base de données
if not exist "gestion_industrielle.db" (
    echo 🗄️ Initialisation de la base de données FastAPI...
    python fastapi_init_db.py
    if errorlevel 1 (
        echo ❌ Erreur lors de l'initialisation de la base de données
        pause
        exit /b 1
    )
)

echo ✅ Base de données OK
echo.

REM Démarrer l'application FastAPI
echo 🚀 Démarrage de l'application FastAPI...
echo 📱 Application accessible à: http://127.0.0.1:8000
echo 📚 Documentation Swagger: http://127.0.0.1:8000/api/docs
echo 📖 Documentation ReDoc: http://127.0.0.1:8000/api/redoc
echo 🔄 Mode reload activé
echo ⏹️ Appuyez sur Ctrl+C pour arrêter
echo.
echo ================================================
echo.

REM Ouvrir le navigateur après 3 secondes
start "" cmd /c "timeout /t 3 /nobreak >nul && start http://127.0.0.1:8000"
start "" cmd /c "timeout /t 5 /nobreak >nul && start http://127.0.0.1:8000/api/docs"

REM Démarrer FastAPI avec Uvicorn
python -m uvicorn fastapi_app:app --host 0.0.0.0 --port 8000 --reload --log-level info

echo.
echo 👋 Application FastAPI arrêtée
pause
