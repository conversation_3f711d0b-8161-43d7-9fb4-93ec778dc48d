<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Application de Gestion Industrielle - FastAPI{% endblock %}</title>
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🚀</text></svg>">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="stylesheet" href="/static/css/style.css">
    <style>
        /* Styles spécifiques FastAPI */
        .fastapi-badge {
            background: linear-gradient(135deg, #009688, #4CAF50);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: 600;
            margin-left: 10px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .api-docs-link {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: linear-gradient(135deg, #FF6B35, #F7931E);
            color: white;
            padding: 12px 16px;
            border-radius: 50px;
            text-decoration: none;
            box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
            transition: all 0.3s ease;
            z-index: 1000;
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 600;
        }
        
        .api-docs-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 107, 53, 0.4);
            text-decoration: none;
            color: white;
        }
        
        .performance-indicator {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(76, 175, 80, 0.2);
            color: #4CAF50;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.7rem;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="logo">
                <h1>
                    <i class="fas fa-rocket"></i> 
                    Gestion Industrielle
                    <span class="fastapi-badge">FastAPI</span>
                </h1>
                <div class="performance-indicator">
                    <i class="fas fa-tachometer-alt"></i> High Performance
                </div>
            </div>
            
            <div class="nav-links">
                <a href="/" class="nav-item {% if request.url.path == '/' %}active{% endif %}">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Tableau de Bord</span>
                </a>
                
                <div class="module-title">Modules</div>
                
                <a href="/maintenance" class="nav-item {% if request.url.path == '/maintenance' %}active{% endif %}">
                    <i class="fas fa-tools"></i>
                    <span>Gestion Maintenance</span>
                </a>
                
                <a href="/production" class="nav-item {% if request.url.path == '/production' %}active{% endif %}">
                    <i class="fas fa-cogs"></i>
                    <span>Gestion Production</span>
                </a>
                
                <a href="/personnel" class="nav-item {% if request.url.path == '/personnel' %}active{% endif %}">
                    <i class="fas fa-users"></i>
                    <span>Gestion Personnel</span>
                </a>
                
                <div class="module-title">API & Documentation</div>
                
                <a href="/api/docs" target="_blank" class="nav-item">
                    <i class="fas fa-book"></i>
                    <span>API Swagger</span>
                    <i class="fas fa-external-link-alt" style="margin-left: auto; font-size: 0.8rem;"></i>
                </a>
                
                <a href="/api/redoc" target="_blank" class="nav-item">
                    <i class="fas fa-file-alt"></i>
                    <span>API ReDoc</span>
                    <i class="fas fa-external-link-alt" style="margin-left: auto; font-size: 0.8rem;"></i>
                </a>
                
                <div class="module-title">Aide</div>
                
                <a href="/help" class="nav-item {% if request.url.path == '/help' %}active{% endif %}">
                    <i class="fas fa-question-circle"></i>
                    <span>Documentation</span>
                </a>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="main-content">
            {% block content %}{% endblock %}
        </div>
    </div>

    <!-- Lien flottant vers la documentation API -->
    <a href="/api/docs" target="_blank" class="api-docs-link">
        <i class="fas fa-code"></i>
        <span>API Docs</span>
    </a>

    <script src="/static/js/main.js"></script>
    <script>
        // Configuration spécifique FastAPI
        window.FASTAPI_CONFIG = {
            baseURL: window.location.origin,
            apiPrefix: '/api',
            docsURL: '/api/docs',
            redocURL: '/api/redoc'
        };
        
        // Fonction pour faire des requêtes à l'API FastAPI
        window.fastAPIRequest = async function(endpoint, options = {}) {
            const url = `${window.FASTAPI_CONFIG.baseURL}${window.FASTAPI_CONFIG.apiPrefix}${endpoint}`;
            
            const defaultOptions = {
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            };
            
            const finalOptions = { ...defaultOptions, ...options };
            
            try {
                const response = await fetch(url, finalOptions);
                
                if (!response.ok) {
                    const errorData = await response.json().catch(() => ({ detail: 'Erreur inconnue' }));
                    throw new Error(errorData.detail || `Erreur HTTP ${response.status}`);
                }
                
                return await response.json();
            } catch (error) {
                console.error('Erreur API FastAPI:', error);
                throw error;
            }
        };
        
        // Notification de performance FastAPI
        document.addEventListener('DOMContentLoaded', function() {
            // Afficher une notification de bienvenue FastAPI
            setTimeout(() => {
                if (window.industrialApp) {
                    window.industrialApp.showNotification(
                        '🚀 Application FastAPI chargée avec succès! Performance optimisée.',
                        'success',
                        3000
                    );
                }
            }, 1000);
        });
    </script>
    {% block scripts %}{% endblock %}
</body>
</html>
