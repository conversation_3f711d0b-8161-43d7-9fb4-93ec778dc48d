"""
Configuration de l'application de gestion industrielle
"""

import os
from pathlib import Path

class Config:
    """Configuration de base"""
    
    # Chemin de base
    BASE_DIR = Path(__file__).parent.absolute()
    
    # Base de données
    DATABASE_URL = f"sqlite:///{BASE_DIR / 'gestion_industrielle.db'}"
    SQLALCHEMY_DATABASE_URI = DATABASE_URL
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # Sécurité
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key-change-in-production'
    
    # Flask
    DEBUG = True
    HOST = '0.0.0.0'
    PORT = 5000
    
    # Application
    APP_NAME = "Application de Gestion Industrielle"
    APP_VERSION = "1.0.0"
    
    # Pagination
    ITEMS_PER_PAGE = 50
    
    # Refresh automatique (en millisecondes)
    AUTO_REFRESH_INTERVAL = 300000  # 5 minutes
    
    # Notifications
    NOTIFICATION_DURATION = 4000  # 4 secondes
    
    # Alertes
    STOCK_CRITICAL_THRESHOLD = 1.0  # Multiplicateur du stock minimum
    MAINTENANCE_OVERDUE_DAYS = 7    # Jours de retard pour alerte maintenance

class DevelopmentConfig(Config):
    """Configuration de développement"""
    DEBUG = True
    SQLALCHEMY_ECHO = False  # Mettre à True pour voir les requêtes SQL

class ProductionConfig(Config):
    """Configuration de production"""
    DEBUG = False
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'production-secret-key'
    HOST = '127.0.0.1'  # Plus sécurisé en production
    
    # Désactiver l'écho SQL en production
    SQLALCHEMY_ECHO = False

class TestingConfig(Config):
    """Configuration de test"""
    TESTING = True
    DATABASE_URL = "sqlite:///:memory:"
    SQLALCHEMY_DATABASE_URI = DATABASE_URL
    SECRET_KEY = 'test-secret-key'

# Configuration par défaut
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}

def get_config(config_name=None):
    """Récupérer la configuration selon l'environnement"""
    if config_name is None:
        config_name = os.environ.get('FLASK_ENV', 'default')
    
    return config.get(config_name, config['default'])
