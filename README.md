# Application de Gestion Industrielle

Une application web complète pour la gestion industrielle développée avec Flask et SQLAlchemy, incluant une base de données locale SQLite.

## 🚀 Fonctionnalités

### 📊 Tableau de Bord
- **KPIs en temps réel** : Taux de panne, production journalière, absentéisme, niveaux de stock
- **Graphiques de performance** : Visualisation des performances par module
- **Alertes automatiques** : Notifications pour stocks critiques, maintenances en retard, etc.
- **Interconnexions** : Vue d'ensemble des relations entre les modules

### 🔧 Module Maintenance
- **Gestion des équipements** : Inventaire complet avec statuts et planification
- **Historique des interventions** : Suivi des réparations et coûts
- **Pièces de rechange** : Gestion des stocks avec alertes de niveau bas
- **Maintenance préventive** : Planning et rappels automatiques

### 🏭 Module Production
- **Production journalière** : Suivi des outputs par ligne et équipe
- **Gestion de stock** : Niveaux des produits finis et matières premières
- **Indicateurs de performance** : Efficacité, qualité, temps d'arrêt

### 👥 Module Personnel
- **Gestion des employés** : Fiches complètes avec postes et départements
- **Système de pointage** : Heures d'arrivée/départ, heures supplémentaires
- **Gestion des congés** : Demandes, approbations, soldes

## 🛠️ Technologies Utilisées

- **Backend** : Flask 3.1.1, SQLAlchemy 2.0.41
- **Base de données** : SQLite (locale)
- **Frontend** : HTML5, CSS3, JavaScript (Vanilla)
- **Graphiques** : Chart.js
- **Icons** : Font Awesome 6.4.0

## 📁 Structure du Projet

```
├── app.py                 # Application Flask principale
├── models.py              # Modèles de base de données
├── database.py            # Configuration de la base de données
├── init_db.py             # Script d'initialisation avec données d'exemple
├── requirements.txt       # Dépendances Python
├── templates/             # Templates HTML
│   ├── base.html         # Template de base
│   ├── dashboard.html    # Page tableau de bord
│   ├── maintenance.html  # Page module maintenance
│   ├── production.html   # Page module production
│   └── personnel.html    # Page module personnel
├── static/               # Fichiers statiques
│   ├── css/
│   │   └── style.css    # Styles CSS
│   └── js/
│       └── main.js      # JavaScript principal
└── gestion_industrielle.db  # Base de données SQLite (créée automatiquement)
```

## 🚀 Installation et Démarrage

### Prérequis
- Python 3.8 ou supérieur
- pip (gestionnaire de paquets Python)

### Installation

1. **Cloner ou télécharger le projet**
   ```bash
   cd "chemin/vers/le/projet"
   ```

2. **Installer les dépendances**
   ```bash
   pip install -r requirements.txt
   ```

3. **Initialiser la base de données avec des données d'exemple**
   ```bash
   python init_db.py
   ```

4. **Lancer l'application**
   ```bash
   python app.py
   ```

5. **Accéder à l'application**
   Ouvrir un navigateur et aller à : `http://127.0.0.1:5000`

## 📊 Données d'Exemple

L'application est livrée avec un jeu de données d'exemple comprenant :

- **5 équipements** avec différents statuts et historiques de maintenance
- **Interventions de maintenance** (préventives, correctives, urgentes)
- **Pièces de rechange** avec niveaux de stock variés
- **30 jours de données de production** sur 3 lignes et 3 équipes
- **8 employés** avec différents postes et départements
- **15 jours de pointage** avec présences, absences et retards
- **Demandes de congés** avec différents statuts

## 🔧 Configuration

### Base de Données
La base de données SQLite est créée automatiquement dans le fichier `gestion_industrielle.db`. 
Pour réinitialiser complètement la base de données :

```bash
python init_db.py
```

### Personnalisation
- **Styles** : Modifier `static/css/style.css`
- **Fonctionnalités** : Ajouter de nouvelles routes dans `app.py`
- **Modèles** : Étendre les modèles dans `models.py`

## 📱 Interface Utilisateur

### Navigation
- **Sidebar responsive** avec navigation entre modules
- **Onglets** pour organiser les fonctionnalités de chaque module
- **Design moderne** avec thème sombre et animations

### Fonctionnalités Interactives
- **Tableaux dynamiques** avec tri et filtrage
- **Graphiques interactifs** avec Chart.js
- **Alertes en temps réel** avec codes couleur
- **Refresh automatique** des données toutes les 5 minutes

## 🔄 API REST

L'application expose plusieurs endpoints API :

### Dashboard
- `GET /api/dashboard/kpis` - KPIs principaux
- `GET /api/dashboard/performance` - Données de performance
- `GET /api/dashboard/alerts` - Alertes récentes

### Maintenance
- `GET /api/maintenance/equipments` - Liste des équipements
- `GET /api/maintenance/interventions` - Interventions récentes
- `GET /api/maintenance/spare-parts` - Pièces de rechange

### Production
- `GET /api/production/daily` - Production journalière
- `GET /api/production/stocks` - Niveaux de stock

### Personnel
- `GET /api/personnel/employees` - Liste des employés
- `GET /api/personnel/attendance` - Données de pointage
- `GET /api/personnel/leaves` - Congés

## 🔒 Sécurité

- **Validation des données** côté serveur
- **Protection CSRF** intégrée avec Flask
- **Gestion d'erreurs** robuste avec messages utilisateur

## 📈 Évolutions Possibles

- **Authentification utilisateur** avec rôles et permissions
- **Export de données** en PDF/Excel
- **Notifications push** pour les alertes critiques
- **API mobile** pour application smartphone
- **Intégration IoT** pour données en temps réel
- **Rapports avancés** avec analyses prédictives

## 🐛 Dépannage

### Problèmes Courants

1. **Erreur de base de données** : Supprimer `gestion_industrielle.db` et relancer `python init_db.py`
2. **Port 5000 occupé** : Modifier le port dans `app.py` ligne finale
3. **Dépendances manquantes** : Vérifier l'installation avec `pip list`

### Logs
Les logs de l'application Flask s'affichent dans le terminal. En mode debug, les erreurs détaillées sont visibles.

## 📞 Support

Pour toute question ou problème, consulter les logs de l'application ou vérifier la configuration des dépendances Python.

---

**Version** : 1.0.0  
**Dernière mise à jour** : Juillet 2025
