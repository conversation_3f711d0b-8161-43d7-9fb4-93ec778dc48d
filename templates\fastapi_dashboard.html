{% extends "fastapi_base.html" %}

{% block title %}Tableau de Bord FastAPI - {{ super() }}{% endblock %}

{% block content %}
<div class="header">
    <h2>
        <i class="fas fa-tachometer-alt"></i> 
        Tableau de Bord
        <span class="fastapi-badge">FastAPI</span>
    </h2>
    <div class="user-info">
        <img src="https://ui-avatars.com/api/?name=FastAPI&background=009688&color=fff" alt="User">
        <span>Administrateur FastAPI</span>
    </div>
</div>

<!-- KPI Cards -->
<div class="kpi-cards">
    <div class="kpi-card">
        <div class="kpi-header">
            <div class="kpi-title">Taux de Panne</div>
            <div class="kpi-icon" style="background: rgba(231, 76, 60, 0.2); color: #e74c3c;">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
        </div>
        <div class="kpi-value" id="breakdown-rate">--</div>
        <div class="kpi-trend trend-down">
            <i class="fas fa-arrow-down"></i>
            <span>vs mois dernier</span>
        </div>
    </div>
    
    <div class="kpi-card">
        <div class="kpi-header">
            <div class="kpi-title">Production Journalière</div>
            <div class="kpi-icon" style="background: rgba(46, 204, 113, 0.2); color: #2ecc71;">
                <i class="fas fa-chart-line"></i>
            </div>
        </div>
        <div class="kpi-value" id="daily-production">--</div>
        <div class="kpi-trend trend-up">
            <i class="fas fa-arrow-up"></i>
            <span>vs hier</span>
        </div>
    </div>
    
    <div class="kpi-card">
        <div class="kpi-header">
            <div class="kpi-title">Taux d'Absentéisme</div>
            <div class="kpi-icon" style="background: rgba(155, 89, 182, 0.2); color: #9b59b6;">
                <i class="fas fa-user-clock"></i>
            </div>
        </div>
        <div class="kpi-value" id="absenteeism-rate">--</div>
        <div class="kpi-trend trend-down">
            <i class="fas fa-arrow-down"></i>
            <span>vs mois dernier</span>
        </div>
    </div>
    
    <div class="kpi-card">
        <div class="kpi-header">
            <div class="kpi-title">Niveau Stock Moyen</div>
            <div class="kpi-icon" style="background: rgba(52, 152, 219, 0.2); color: #3498db;">
                <i class="fas fa-boxes"></i>
            </div>
        </div>
        <div class="kpi-value" id="avg-stock-level">--</div>
        <div class="kpi-trend trend-up">
            <i class="fas fa-arrow-up"></i>
            <span>vs semaine dernière</span>
        </div>
    </div>
</div>

<!-- Charts -->
<div class="charts-row">
    <div class="chart-container">
        <div class="chart-title">
            <i class="fas fa-chart-bar"></i> 
            Performance des Modules
            <span style="font-size: 0.8rem; color: #4CAF50; margin-left: 10px;">
                <i class="fas fa-bolt"></i> FastAPI Powered
            </span>
        </div>
        <div class="chart">
            <canvas id="performanceChart"></canvas>
        </div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title"><i class="fas fa-bell"></i> Alertes Récentes</div>
        <div class="alerts" id="alerts-container">
            <div class="loading">Chargement des alertes...</div>
        </div>
    </div>
</div>

<!-- Graphique de tendance de production -->
<div class="chart-container" style="margin-top: 20px;">
    <div class="chart-title">
        <i class="fas fa-chart-line"></i> 
        Tendance de Production (7 derniers jours)
        <span style="font-size: 0.8rem; color: #FF6B35; margin-left: 10px;">
            <i class="fas fa-rocket"></i> API REST
        </span>
    </div>
    <div class="chart">
        <canvas id="productionTrendChart"></canvas>
    </div>
</div>

<!-- Avantages FastAPI -->
<div class="interconnections">
    <h3><i class="fas fa-rocket"></i> Avantages FastAPI</h3>
    <div class="connections-container">
        <div class="connection">
            <div class="connection-title">Performance ⚡</div>
            <div class="connection-description">
                FastAPI offre des performances exceptionnelles, comparables à NodeJS et Go, 
                grâce à Starlette et Pydantic.
            </div>
        </div>
        
        <div class="connection">
            <div class="connection-title">Documentation Automatique 📚</div>
            <div class="connection-description">
                Documentation interactive automatique avec Swagger UI et ReDoc, 
                basée sur les standards OpenAPI.
            </div>
        </div>
        
        <div class="connection">
            <div class="connection-title">Validation des Données ✅</div>
            <div class="connection-description">
                Validation automatique des données avec Pydantic, 
                réduction des erreurs et meilleure robustesse.
            </div>
        </div>
        
        <div class="connection">
            <div class="connection-title">Type Hints Natifs 🔍</div>
            <div class="connection-description">
                Support complet des type hints Python pour une meilleure 
                expérience de développement et moins d'erreurs.
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Charger les KPIs avec FastAPI
    window.fastAPIRequest('/dashboard/kpis')
        .then(data => {
            document.getElementById('breakdown-rate').textContent = data.breakdown_rate + '%';
            document.getElementById('daily-production').textContent = data.daily_production.toLocaleString();
            document.getElementById('absenteeism-rate').textContent = data.absenteeism_rate + '%';
            document.getElementById('avg-stock-level').textContent = data.avg_stock_level + '%';
        })
        .catch(error => {
            console.error('Erreur lors du chargement des KPIs:', error);
            window.industrialApp.showNotification('Erreur lors du chargement des KPIs', 'error');
        });
    
    // Charger les données de performance
    window.fastAPIRequest('/dashboard/performance')
        .then(data => {
            const ctx = document.getElementById('performanceChart').getContext('2d');
            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['Maintenance', 'Production', 'Personnel'],
                    datasets: [{
                        label: 'Performance (%)',
                        data: [data.maintenance, data.production, data.personnel],
                        backgroundColor: [
                            'rgba(52, 152, 219, 0.7)',
                            'rgba(46, 204, 113, 0.7)',
                            'rgba(155, 89, 182, 0.7)'
                        ],
                        borderColor: [
                            'rgba(52, 152, 219, 1)',
                            'rgba(46, 204, 113, 1)',
                            'rgba(155, 89, 182, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            grid: { color: 'rgba(255, 255, 255, 0.1)' },
                            ticks: { color: '#ecf0f1' }
                        },
                        x: {
                            grid: { display: false },
                            ticks: { color: '#ecf0f1' }
                        }
                    },
                    plugins: { 
                        legend: { display: false },
                        title: {
                            display: true,
                            text: 'Powered by FastAPI',
                            color: '#4CAF50',
                            font: { size: 10 }
                        }
                    }
                }
            });
        })
        .catch(error => {
            console.error('Erreur lors du chargement des performances:', error);
            window.industrialApp.showNotification('Erreur lors du chargement des performances', 'error');
        });
    
    // Charger le graphique de tendance de production
    window.fastAPIRequest('/dashboard/production-trend')
        .then(data => {
            const ctx = document.getElementById('productionTrendChart').getContext('2d');
            new Chart(ctx, {
                type: 'line',
                data: data,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: { color: 'rgba(255, 255, 255, 0.1)' },
                            ticks: { color: '#ecf0f1' }
                        },
                        x: {
                            grid: { color: 'rgba(255, 255, 255, 0.1)' },
                            ticks: { color: '#ecf0f1' }
                        }
                    },
                    plugins: {
                        legend: {
                            labels: { color: '#ecf0f1' }
                        },
                        title: {
                            display: true,
                            text: 'API REST FastAPI',
                            color: '#FF6B35',
                            font: { size: 10 }
                        }
                    }
                }
            });
        })
        .catch(error => {
            console.error('Erreur lors du chargement de la tendance:', error);
            window.industrialApp.showNotification('Erreur lors du chargement de la tendance', 'error');
        });
    
    // Charger les alertes
    window.fastAPIRequest('/dashboard/alerts')
        .then(data => {
            const container = document.getElementById('alerts-container');
            if (data.length === 0) {
                container.innerHTML = '<div class="no-alerts">Aucune alerte récente</div>';
            } else {
                container.innerHTML = data.map(alert => `
                    <div class="alert-item">
                        <i class="${alert.icon}" style="color: ${alert.color};"></i>
                        ${alert.message}
                    </div>
                `).join('');
            }
        })
        .catch(error => {
            console.error('Erreur lors du chargement des alertes:', error);
            document.getElementById('alerts-container').innerHTML = '<div class="error">Erreur de chargement</div>';
        });
});
</script>
{% endblock %}
