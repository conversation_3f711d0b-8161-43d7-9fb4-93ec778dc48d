from flask import Flask
from models import db
import os

def create_app():
    """Factory pour créer l'application Flask"""
    app = Flask(__name__)
    
    # Configuration de la base de données
    basedir = os.path.abspath(os.path.dirname(__file__))
    app.config['SQLALCHEMY_DATABASE_URI'] = f'sqlite:///{os.path.join(basedir, "gestion_industrielle.db")}'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    app.config['SECRET_KEY'] = 'votre-cle-secrete-ici'
    
    # Initialiser la base de données
    db.init_app(app)
    
    return app

def init_database(app):
    """Initialiser la base de données avec les tables"""
    with app.app_context():
        db.create_all()
        print("Base de données initialisée avec succès!")

def reset_database(app):
    """Réinitialiser complètement la base de données"""
    with app.app_context():
        db.drop_all()
        db.create_all()
        print("Base de données réinitialisée avec succès!")
