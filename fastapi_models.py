"""
Modèles SQLAlchemy pour FastAPI
"""

from sqlalchemy import Column, Integer, String, Float, Date, DateTime, Time, Text, ForeignKey, func
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime

Base = declarative_base()

class Equipment(Base):
    """Modèle pour les équipements de maintenance"""
    __tablename__ = 'equipments'
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False)
    code = Column(String(50), unique=True, nullable=False, index=True)
    type = Column(String(50), nullable=False)
    location = Column(String(100), nullable=False)
    status = Column(String(20), default='Opérationnel')
    installation_date = Column(Date, nullable=False)
    last_maintenance = Column(Date)
    next_maintenance = Column(Date)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relations
    interventions = relationship("Intervention", back_populates="equipment", cascade="all, delete-orphan")
    spare_parts = relationship("SparePart", back_populates="equipment", cascade="all, delete-orphan")

class Intervention(Base):
    """Modèle pour les interventions de maintenance"""
    __tablename__ = 'interventions'
    
    id = Column(Integer, primary_key=True, index=True)
    equipment_id = Column(Integer, ForeignKey('equipments.id'), nullable=False)
    type = Column(String(50), nullable=False)  # Préventive, Corrective, Urgente
    description = Column(Text, nullable=False)
    start_time = Column(DateTime, nullable=False)
    end_time = Column(DateTime)
    technician = Column(String(100), nullable=False)
    cost = Column(Float, default=0.0)
    status = Column(String(20), default='En cours')
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relations
    equipment = relationship("Equipment", back_populates="interventions")

class SparePart(Base):
    """Modèle pour les pièces de rechange"""
    __tablename__ = 'spare_parts'
    
    id = Column(Integer, primary_key=True, index=True)
    equipment_id = Column(Integer, ForeignKey('equipments.id'), nullable=False)
    name = Column(String(100), nullable=False)
    reference = Column(String(50), unique=True, nullable=False, index=True)
    quantity_stock = Column(Integer, default=0)
    quantity_min = Column(Integer, default=5)
    unit_price = Column(Float, nullable=False)
    supplier = Column(String(100))
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relations
    equipment = relationship("Equipment", back_populates="spare_parts")

class Production(Base):
    """Modèle pour la production journalière"""
    __tablename__ = 'productions'
    
    id = Column(Integer, primary_key=True, index=True)
    date = Column(Date, nullable=False, index=True)
    line = Column(String(50), nullable=False)
    shift = Column(String(20), nullable=False)  # Matin, Après-midi, Nuit
    product_type = Column(String(100), nullable=False)
    quantity_produced = Column(Integer, nullable=False)
    quantity_target = Column(Integer, nullable=False)
    quality_rate = Column(Float, default=100.0)
    downtime_minutes = Column(Integer, default=0)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relations
    consumptions = relationship("Consumption", back_populates="production")

class Stock(Base):
    """Modèle pour la gestion de stock"""
    __tablename__ = 'stocks'
    
    id = Column(Integer, primary_key=True, index=True)
    product_name = Column(String(100), nullable=False)
    product_code = Column(String(50), unique=True, nullable=False, index=True)
    category = Column(String(50), nullable=False)  # Matière première, Produit fini, Semi-fini
    quantity_current = Column(Integer, default=0)
    quantity_min = Column(Integer, default=10)
    quantity_max = Column(Integer, default=1000)
    unit = Column(String(20), default='pcs')
    location = Column(String(100))
    last_movement = Column(DateTime)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relations
    consumptions = relationship("Consumption", back_populates="stock")

class Consumption(Base):
    """Modèle pour les consommations de matières premières"""
    __tablename__ = 'consumptions'
    
    id = Column(Integer, primary_key=True, index=True)
    stock_id = Column(Integer, ForeignKey('stocks.id'), nullable=False)
    production_id = Column(Integer, ForeignKey('productions.id'), nullable=False)
    quantity_consumed = Column(Integer, nullable=False)
    date = Column(Date, nullable=False, index=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relations
    stock = relationship("Stock", back_populates="consumptions")
    production = relationship("Production", back_populates="consumptions")

class Employee(Base):
    """Modèle pour les employés"""
    __tablename__ = 'employees'
    
    id = Column(Integer, primary_key=True, index=True)
    employee_id = Column(String(20), unique=True, nullable=False, index=True)
    first_name = Column(String(50), nullable=False)
    last_name = Column(String(50), nullable=False)
    position = Column(String(100), nullable=False)
    department = Column(String(50), nullable=False)
    hire_date = Column(Date, nullable=False)
    salary = Column(Float)
    status = Column(String(20), default='Actif')
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relations
    time_records = relationship("TimeRecord", back_populates="employee", cascade="all, delete-orphan")
    leaves = relationship("Leave", back_populates="employee", cascade="all, delete-orphan")

class TimeRecord(Base):
    """Modèle pour le pointage"""
    __tablename__ = 'time_records'
    
    id = Column(Integer, primary_key=True, index=True)
    employee_id = Column(Integer, ForeignKey('employees.id'), nullable=False)
    date = Column(Date, nullable=False, index=True)
    check_in = Column(Time)
    check_out = Column(Time)
    break_duration = Column(Integer, default=0)  # en minutes
    overtime_hours = Column(Float, default=0.0)
    status = Column(String(20), default='Présent')  # Présent, Absent, Retard
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relations
    employee = relationship("Employee", back_populates="time_records")

class Leave(Base):
    """Modèle pour les congés"""
    __tablename__ = 'leaves'
    
    id = Column(Integer, primary_key=True, index=True)
    employee_id = Column(Integer, ForeignKey('employees.id'), nullable=False)
    type = Column(String(50), nullable=False)  # Congé payé, Maladie, Formation
    start_date = Column(Date, nullable=False)
    end_date = Column(Date, nullable=False)
    days_count = Column(Integer, nullable=False)
    status = Column(String(20), default='En attente')  # En attente, Approuvé, Refusé
    reason = Column(Text)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relations
    employee = relationship("Employee", back_populates="leaves")
