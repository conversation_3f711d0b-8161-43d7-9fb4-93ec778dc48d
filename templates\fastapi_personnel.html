{% extends "fastapi_base.html" %}

{% block title %}Gestion Personnel FastAPI - {{ super() }}{% endblock %}

{% block content %}
<div class="header">
    <h2>
        <i class="fas fa-users"></i> 
        Gestion de Personnel
        <span class="fastapi-badge">FastAPI</span>
    </h2>
    <div class="user-info">
        <img src="https://ui-avatars.com/api/?name=Personnel&background=9b59b6&color=fff" alt="User">
        <span>Responsable RH</span>
    </div>
</div>

<!-- Onglets de navigation -->
<div class="tabs-container">
    <div class="tabs">
        <button class="tab-button active" data-tab="employees">
            <i class="fas fa-id-badge"></i> Liste du Personnel
        </button>
        <button class="tab-button" data-tab="attendance">
            <i class="fas fa-fingerprint"></i> Pointage
        </button>
        <button class="tab-button" data-tab="leaves">
            <i class="fas fa-umbrella-beach"></i> Congés
        </button>
    </div>
</div>

<!-- Contenu des onglets -->
<div class="tab-content">
    <!-- Onglet Liste du Personnel -->
    <div id="employees" class="tab-pane active">
        <div class="section-header">
            <h3><i class="fas fa-id-badge"></i> Employés Actifs</h3>
            <div class="timestamp"></div>
        </div>
        <div class="data-table">
            <div id="employees-table" class="loading">Chargement des employés...</div>
        </div>
    </div>

    <!-- Onglet Pointage -->
    <div id="attendance" class="tab-pane">
        <div class="section-header">
            <h3><i class="fas fa-fingerprint"></i> Pointage des 7 Derniers Jours</h3>
            <div class="timestamp"></div>
        </div>
        <div class="data-table">
            <div id="attendance-table" class="loading">Chargement du pointage...</div>
        </div>
    </div>

    <!-- Onglet Congés -->
    <div id="leaves" class="tab-pane">
        <div class="section-header">
            <h3><i class="fas fa-umbrella-beach"></i> Demandes de Congés</h3>
            <div class="timestamp"></div>
        </div>
        <div class="data-table">
            <div id="leaves-table" class="loading">Chargement des congés...</div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Gestion des onglets
    const tabButtons = document.querySelectorAll('.tab-button');
    const tabPanes = document.querySelectorAll('.tab-pane');

    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            const tabId = button.dataset.tab;
            
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabPanes.forEach(pane => pane.classList.remove('active'));
            
            button.classList.add('active');
            document.getElementById(tabId).classList.add('active');
            
            loadTabData(tabId);
        });
    });

    loadPersonnelData();
});

function loadPersonnelData() {
    loadEmployees();
    loadAttendance();
    loadLeaves();
}

function loadTabData(tabId) {
    switch(tabId) {
        case 'employees':
            loadEmployees();
            break;
        case 'attendance':
            loadAttendance();
            break;
        case 'leaves':
            loadLeaves();
            break;
    }
}

function loadEmployees() {
    window.fastAPIRequest('/personnel/employees')
        .then(data => {
            const columns = [
                { field: 'employee_id', title: 'ID' },
                { 
                    field: 'first_name', 
                    title: 'Nom Complet',
                    render: (value, row) => `${row.first_name} ${row.last_name}`
                },
                { field: 'position', title: 'Poste' },
                { field: 'department', title: 'Département' },
                { 
                    field: 'hire_date', 
                    title: 'Date d\'Embauche',
                    render: (value) => window.industrialApp.formatDate(value)
                },
                { 
                    field: 'salary', 
                    title: 'Salaire (€)',
                    render: (value) => value ? window.industrialApp.formatNumber(value) : '-'
                },
                { 
                    field: 'status', 
                    title: 'Statut',
                    render: (value) => window.industrialApp.getStatusBadge(value)
                }
            ];
            
            window.industrialApp.createDataTable('employees-table', data, columns);
        })
        .catch(error => {
            console.error('Erreur lors du chargement des employés:', error);
            document.getElementById('employees-table').innerHTML = '<div class="error">Erreur de chargement</div>';
        });
}

function loadAttendance() {
    window.fastAPIRequest('/personnel/attendance')
        .then(data => {
            const columns = [
                { 
                    field: 'employee', 
                    title: 'Employé',
                    render: (value) => `${value.first_name} ${value.last_name}`
                },
                { 
                    field: 'date', 
                    title: 'Date',
                    render: (value) => window.industrialApp.formatDate(value)
                },
                { field: 'check_in', title: 'Arrivée', render: (value) => value || '-' },
                { field: 'check_out', title: 'Départ', render: (value) => value || '-' },
                { field: 'break_duration', title: 'Pause (min)', render: (value) => value || '-' },
                { 
                    field: 'overtime_hours', 
                    title: 'Heures Sup.',
                    render: (value) => value > 0 ? window.industrialApp.formatNumber(value, 1) + 'h' : '-'
                },
                { 
                    field: 'status', 
                    title: 'Statut',
                    render: (value) => window.industrialApp.getStatusBadge(value)
                }
            ];
            
            window.industrialApp.createDataTable('attendance-table', data, columns);
        })
        .catch(error => {
            console.error('Erreur lors du chargement du pointage:', error);
            document.getElementById('attendance-table').innerHTML = '<div class="error">Erreur de chargement</div>';
        });
}

function loadLeaves() {
    window.fastAPIRequest('/personnel/leaves')
        .then(data => {
            const columns = [
                { 
                    field: 'employee', 
                    title: 'Employé',
                    render: (value) => `${value.first_name} ${value.last_name}`
                },
                { field: 'type', title: 'Type de Congé' },
                { 
                    field: 'start_date', 
                    title: 'Date Début',
                    render: (value) => window.industrialApp.formatDate(value)
                },
                { 
                    field: 'end_date', 
                    title: 'Date Fin',
                    render: (value) => window.industrialApp.formatDate(value)
                },
                { 
                    field: 'days_count', 
                    title: 'Nb Jours',
                    render: (value) => `${value} jour${value > 1 ? 's' : ''}`
                },
                { 
                    field: 'status', 
                    title: 'Statut',
                    render: (value) => window.industrialApp.getStatusBadge(value)
                },
                { field: 'reason', title: 'Motif', render: (value) => value || '-' }
            ];
            
            window.industrialApp.createDataTable('leaves-table', data, columns);
        })
        .catch(error => {
            console.error('Erreur lors du chargement des congés:', error);
            document.getElementById('leaves-table').innerHTML = '<div class="error">Erreur de chargement</div>';
        });
}
</script>
{% endblock %}
