<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Application de Gestion Industrielle - Version HTML</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {
            --primary: #2c3e50;
            --secondary: #3498db;
            --success: #27ae60;
            --warning: #f39c12;
            --danger: #e74c3c;
            --light: #ecf0f1;
            --dark: #34495e;
            --maintenance: #3498db;
            --production: #2ecc71;
            --personnel: #9b59b6;
            --dashboard: #e74c3c;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: '<PERSON><PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #1a2a3a, #2c3e50);
            color: #fff;
            min-height: 100vh;
        }
        
        .container {
            display: flex;
            min-height: 100vh;
        }
        
        /* Sidebar Styles */
        .sidebar {
            width: 250px;
            background: rgba(25, 35, 45, 0.9);
            padding: 20px 0;
            box-shadow: 3px 0 15px rgba(0, 0, 0, 0.2);
            z-index: 10;
        }
        
        .logo {
            text-align: center;
            padding: 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .logo h1 {
            font-size: 1.5rem;
            background: linear-gradient(to right, #3498db, #2ecc71, #9b59b6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            font-weight: 700;
        }
        
        .nav-links {
            padding: 20px 0;
        }
        
        .nav-item {
            padding: 12px 25px;
            display: flex;
            align-items: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .nav-item:hover {
            background: rgba(52, 152, 219, 0.2);
        }
        
        .nav-item.active {
            background: rgba(52, 152, 219, 0.3);
            border-left: 4px solid var(--secondary);
        }
        
        .nav-item i {
            margin-right: 15px;
            width: 20px;
            text-align: center;
        }
        
        .module-title {
            padding: 15px 25px;
            text-transform: uppercase;
            font-size: 0.8rem;
            letter-spacing: 1px;
            color: #7f8c8d;
            margin-top: 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        /* Main Content Styles */
        .main-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }
        
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .header h2 {
            font-size: 1.8rem;
            font-weight: 600;
            display: flex;
            align-items: center;
        }
        
        .header h2 i {
            margin-right: 15px;
            color: var(--secondary);
        }
        
        .user-info {
            display: flex;
            align-items: center;
        }
        
        .user-info img {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-right: 10px;
        }
        
        /* Dashboard Styles */
        .dashboard {
            display: none;
        }
        
        .dashboard.active {
            display: block;
        }
        
        .kpi-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .kpi-card {
            background: rgba(255, 255, 255, 0.08);
            border-radius: 10px;
            padding: 20px;
            display: flex;
            flex-direction: column;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            transition: transform 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.05);
        }
        
        .kpi-card:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.12);
        }
        
        .kpi-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .kpi-title {
            font-size: 1rem;
            color: #bdc3c7;
        }
        
        .kpi-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
        }
        
        .kpi-value {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 5px;
        }
        
        .kpi-trend {
            display: flex;
            align-items: center;
            font-size: 0.9rem;
        }
        
        .trend-up {
            color: var(--success);
        }
        
        .trend-down {
            color: var(--danger);
        }
        
        .charts-row {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .chart-container {
            background: rgba(255, 255, 255, 0.08);
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.05);
        }
        
        .chart-title {
            margin-bottom: 15px;
            font-size: 1.2rem;
            display: flex;
            align-items: center;
        }
        
        .chart-title i {
            margin-right: 10px;
            color: var(--secondary);
        }
        
        .chart {
            height: 300px;
            position: relative;
        }
        
        /* Module Styles */
        .module-section {
            display: none;
            animation: fadeIn 0.5s ease;
        }
        
        .module-section.active {
            display: block;
        }
        
        .module-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .module-card {
            background: rgba(255, 255, 255, 0.08);
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.05);
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }
        
        .module-card:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.12);
        }
        
        .module-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 5px;
            height: 100%;
        }
        
        .maintenance-card::before {
            background: var(--maintenance);
        }
        
        .production-card::before {
            background: var(--production);
        }
        
        .personnel-card::before {
            background: var(--personnel);
        }
        
        .card-icon {
            font-size: 2.5rem;
            margin-bottom: 20px;
        }
        
        .maintenance-card .card-icon {
            color: var(--maintenance);
        }
        
        .production-card .card-icon {
            color: var(--production);
        }
        
        .personnel-card .card-icon {
            color: var(--personnel);
        }
        
        .card-title {
            font-size: 1.3rem;
            margin-bottom: 10px;
            font-weight: 600;
        }
        
        .card-description {
            color: #bdc3c7;
            font-size: 0.95rem;
            line-height: 1.6;
        }
        
        .interconnections {
            margin-top: 30px;
        }
        
        .interconnections h3 {
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }
        
        .interconnections h3 i {
            margin-right: 10px;
            color: var(--warning);
        }
        
        .connections-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .connection {
            background: rgba(255, 255, 255, 0.08);
            border-radius: 10px;
            padding: 20px;
            border-left: 4px solid var(--warning);
        }
        
        .connection-title {
            font-weight: 600;
            margin-bottom: 10px;
            color: var(--warning);
        }
        
        .connection-description {
            font-size: 0.95rem;
            line-height: 1.6;
            color: #bdc3c7;
        }
        
        /* Animations */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        /* Responsive */
        @media (max-width: 992px) {
            .charts-row {
                grid-template-columns: 1fr;
            }
            
            .container {
                flex-direction: column;
            }
            
            .sidebar {
                width: 100%;
                padding: 10px 0;
            }
            
            .nav-links {
                display: flex;
                flex-wrap: wrap;
                justify-content: center;
            }
            
            .nav-item {
                padding: 10px 15px;
            }
            
            .module-title {
                display: none;
            }
        }
        
        @media (max-width: 576px) {
            .header {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .user-info {
                margin-top: 15px;
            }
            
            .kpi-cards {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="logo">
                <h1><i class="fas fa-industry"></i> Gestion Industrielle</h1>
            </div>
            
            <div class="nav-links">
                <div class="nav-item active" data-target="dashboard">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Tableau de Bord</span>
                </div>
                
                <div class="module-title">Modules</div>
                
                <div class="nav-item" data-target="maintenance">
                    <i class="fas fa-tools"></i>
                    <span>Gestion Maintenance</span>
                </div>
                
                <div class="nav-item" data-target="production">
                    <i class="fas fa-cogs"></i>
                    <span>Gestion Production</span>
                </div>
                
                <div class="nav-item" data-target="personnel">
                    <i class="fas fa-users"></i>
                    <span>Gestion Personnel</span>
                </div>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="main-content">
            <!-- Dashboard -->
            <div class="dashboard active" id="dashboard">
                <div class="header">
                    <h2><i class="fas fa-tachometer-alt"></i> Tableau de Bord</h2>
                    <div class="user-info">
                        <img src="https://ui-avatars.com/api/?name=Admin&background=3498db&color=fff" alt="User">
                        <span>Administrateur</span>
                    </div>
                </div>
                
                <!-- KPI Cards -->
                <div class="kpi-cards">
                    <div class="kpi-card">
                        <div class="kpi-header">
                            <div class="kpi-title">Taux de Panne</div>
                            <div class="kpi-icon" style="background: rgba(231, 76, 60, 0.2); color: #e74c3c;">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                        </div>
                        <div class="kpi-value">4.2%</div>
                        <div class="kpi-trend trend-down">
                            <i class="fas fa-arrow-down"></i>
                            <span>0.8% vs mois dernier</span>
                        </div>
                    </div>
                    
                    <div class="kpi-card">
                        <div class="kpi-header">
                            <div class="kpi-title">Production Journalière</div>
                            <div class="kpi-icon" style="background: rgba(46, 204, 113, 0.2); color: #2ecc71;">
                                <i class="fas fa-chart-line"></i>
                            </div>
                        </div>
                        <div class="kpi-value">1,248</div>
                        <div class="kpi-trend trend-up">
                            <i class="fas fa-arrow-up"></i>
                            <span>5.3% vs hier</span>
                        </div>
                    </div>
                    
                    <div class="kpi-card">
                        <div class="kpi-header">
                            <div class="kpi-title">Taux d'Absentéisme</div>
                            <div class="kpi-icon" style="background: rgba(155, 89, 182, 0.2); color: #9b59b6;">
                                <i class="fas fa-user-clock"></i>
                            </div>
                        </div>
                        <div class="kpi-value">2.7%</div>
                        <div class="kpi-trend trend-down">
                            <i class="fas fa-arrow-down"></i>
                            <span>1.1% vs mois dernier</span>
                        </div>
                    </div>
                    
                    <div class="kpi-card">
                        <div class="kpi-header">
                            <div class="kpi-title">Niveau Stock Moyen</div>
                            <div class="kpi-icon" style="background: rgba(52, 152, 219, 0.2); color: #3498db;">
                                <i class="fas fa-boxes"></i>
                            </div>
                        </div>
                        <div class="kpi-value">78%</div>
                        <div class="kpi-trend trend-up">
                            <i class="fas fa-arrow-up"></i>
                            <span>12% vs semaine dernière</span>
                        </div>
                    </div>
                </div>
                
                <!-- Charts -->
                <div class="charts-row">
                    <div class="chart-container">
                        <div class="chart-title"><i class="fas fa-chart-bar"></i> Performance des Modules</div>
                        <div class="chart">
                            <canvas id="performanceChart"></canvas>
                        </div>
                    </div>
                    
                    <div class="chart-container">
                        <div class="chart-title"><i class="fas fa-bell"></i> Alertes Récentes</div>
                        <div class="alerts">
                            <div class="alert-item">
                                <i class="fas fa-exclamation-circle" style="color: #e74c3c;"></i>
                                Stock pièce #P-245 critique
                            </div>
                            <div class="alert-item">
                                <i class="fas fa-exclamation-circle" style="color: #f39c12;"></i>
                                Maintenance préventive Presse HP-202 retardée
                            </div>
                            <div class="alert-item">
                                <i class="fas fa-exclamation-circle" style="color: #3498db;"></i>
                                Production chute de 15% sur ligne 3
                            </div>
                            <div class="alert-item">
                                <i class="fas fa-exclamation-circle" style="color: #9b59b6;"></i>
                                Absentéisme élevé dans l'équipe B
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Interconnections -->
                <div class="interconnections">
                    <h3><i class="fas fa-link"></i> Interconnexions Clés</h3>
                    <div class="connections-container">
                        <div class="connection">
                            <div class="connection-title">Interventions ↔ Équipements</div>
                            <div class="connection-description">
                                Suivi des interventions par équipement avec historique complet et coûts associés.
                            </div>
                        </div>
                        
                        <div class="connection">
                            <div class="connection-title">Consommations ↔ Stock</div>
                            <div class="connection-description">
                                Mise à jour automatique des niveaux de stock basée sur les consommations de production.
                            </div>
                        </div>
                        
                        <div class="connection">
                            <div class="connection-title">Pointage → Production</div>
                            <div class="connection-description">
                                Analyse de productivité basée sur les heures travaillées et la production réalisée.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Maintenance Module -->
            <div class="module-section" id="maintenance">
                <div class="header">
                    <h2><i class="fas fa-tools"></i> Gestion de Maintenance</h2>
                    <div class="user-info">
                        <img src="https://ui-avatars.com/api/?name=Admin&background=3498db&color=fff" alt="User">
                        <span>Responsable Maintenance</span>
                    </div>
                </div>
                
                <div class="module-grid">
                    <div class="module-card maintenance-card">
                        <div class="card-icon">
                            <i class="fas fa-list"></i>
                        </div>
                        <div class="card-title">Liste des Équipements</div>
                        <div class="card-description">
                            Inventaire complet des machines avec détails techniques, localisation et statut.
                        </div>
                    </div>
                    
                    <div class="module-card maintenance-card">
                        <div class="card-icon">
                            <i class="fas fa-history"></i>
                        </div>
                        <div class="card-title">Historique des Équipements</div>
                        <div class="card-description">
                            Suivi des pannes, réparations et coûts d'entretien par équipement.
                        </div>
                    </div>
                    
                    <div class="module-card maintenance-card">
                        <div class="card-icon">
                            <i class="fas fa-cogs"></i>
                        </div>
                        <div class="card-title">Pièces de Rechange</div>
                        <div class="card-description">
                            Gestion du stock de composants avec niveaux d'alerte et commandes automatiques.
                        </div>
                    </div>
                    
                    <div class="module-card maintenance-card">
                        <div class="card-icon">
                            <i class="fas fa-wrench"></i>
                        </div>
                        <div class="card-title">Interventions</div>
                        <div class="card-description">
                            Suivi des actions correctives avec temps d'intervention et ressources utilisées.
                        </div>
                    </div>
                    
                    <div class="module-card maintenance-card">
                        <div class="card-icon">
                            <i class="fas fa-calendar-check"></i>
                        </div>
                        <div class="card-title">Maintenance Préventive</div>
                        <div class="card-description">
                            Planning des contrôles périodiques et alertes pour les interventions programmées.
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Production Module -->
            <div class="module-section" id="production">
                <div class="header">
                    <h2><i class="fas fa-cogs"></i> Gestion de Production</h2>
                    <div class="user-info">
                        <img src="https://ui-avatars.com/api/?name=Admin&background=2ecc71&color=fff" alt="User">
                        <span>Responsable Production</span>
                    </div>
                </div>
                
                <div class="module-grid">
                    <div class="module-card production-card">
                        <div class="card-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="card-title">Production Journalière</div>
                        <div class="card-description">
                            Suivi quotidien des outputs par ligne de production, poste et équipe.
                        </div>
                    </div>
                    
                    <div class="module-card production-card">
                        <div class="card-icon">
                            <i class="fas fa-warehouse"></i>
                        </div>
                        <div class="card-title">Gestion de Stock</div>
                        <div class="card-description">
                            Niveaux de stock des produits finis et semi-finis avec rotation et seuils d'alerte.
                        </div>
                    </div>
                    
                    <div class="module-card production-card">
                        <div class="card-icon">
                            <i class="fas fa-gas-pump"></i>
                        </div>
                        <div class="card-title">Consommations</div>
                        <div class="card-description">
                            Traçabilité des matières premières utilisées et corrélation avec la production.
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Personnel Module -->
            <div class="module-section" id="personnel">
                <div class="header">
                    <h2><i class="fas fa-users"></i> Gestion de Personnel</h2>
                    <div class="user-info">
                        <img src="https://ui-avatars.com/api/?name=Admin&background=9b59b6&color=fff" alt="User">
                        <span>Responsable RH</span>
                    </div>
                </div>
                
                <div class="module-grid">
                    <div class="module-card personnel-card">
                        <div class="card-icon">
                            <i class="fas fa-id-badge"></i>
                        </div>
                        <div class="card-title">Liste du Personnel</div>
                        <div class="card-description">
                            Fiches employés avec postes, compétences, formations et historiques.
                        </div>
                    </div>
                    
                    <div class="module-card personnel-card">
                        <div class="card-icon">
                            <i class="fas fa-fingerprint"></i>
                        </div>
                        <div class="card-title">Pointage</div>
                        <div class="card-description">
                            Enregistrement des heures de travail, retards et heures supplémentaires.
                        </div>
                    </div>
                    
                    <div class="module-card personnel-card">
                        <div class="card-icon">
                            <i class="fas fa-umbrella-beach"></i>
                        </div>
                        <div class="card-title">État des Congés</div>
                        <div class="card-description">
                            Solde des congés, planning des absences et gestion des demandes.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Navigation
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', function() {
                // Remove active class from all
                document.querySelectorAll('.nav-item').forEach(nav => {
                    nav.classList.remove('active');
                });
                
                // Add active class to clicked
                this.classList.add('active');
                
                // Hide all sections
                document.querySelectorAll('.dashboard, .module-section').forEach(section => {
                    section.classList.remove('active');
                });
                
                // Show target section
                const target = this.getAttribute('data-target');
                document.getElementById(target).classList.add('active');
            });
        });
        
        // Charts
        document.addEventListener('DOMContentLoaded', function() {
            // Performance Chart
            const ctx = document.getElementById('performanceChart').getContext('2d');
            const performanceChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['Maintenance', 'Production', 'Personnel'],
                    datasets: [{
                        label: 'Performance (%)',
                        data: [88, 92, 78],
                        backgroundColor: [
                            'rgba(52, 152, 219, 0.7)',
                            'rgba(46, 204, 113, 0.7)',
                            'rgba(155, 89, 182, 0.7)'
                        ],
                        borderColor: [
                            'rgba(52, 152, 219, 1)',
                            'rgba(46, 204, 113, 1)',
                            'rgba(155, 89, 182, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            },
                            ticks: {
                                color: '#ecf0f1'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            },
                            ticks: {
                                color: '#ecf0f1'
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });
        });
    </script>
</body>
</html>