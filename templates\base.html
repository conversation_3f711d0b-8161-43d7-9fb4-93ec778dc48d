<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Application de Gestion Industrielle{% endblock %}</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body>
    <div class="container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="logo">
                <h1><i class="fas fa-industry"></i> Gestion Industrielle</h1>
            </div>
            
            <div class="nav-links">
                <a href="{{ url_for('dashboard') }}" class="nav-item {% if request.endpoint == 'dashboard' %}active{% endif %}">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Tableau de Bord</span>
                </a>
                
                <div class="module-title">Modules</div>
                
                <a href="{{ url_for('maintenance') }}" class="nav-item {% if request.endpoint == 'maintenance' %}active{% endif %}">
                    <i class="fas fa-tools"></i>
                    <span>Gestion Maintenance</span>
                </a>
                
                <a href="{{ url_for('production') }}" class="nav-item {% if request.endpoint == 'production' %}active{% endif %}">
                    <i class="fas fa-cogs"></i>
                    <span>Gestion Production</span>
                </a>
                
                <a href="{{ url_for('personnel') }}" class="nav-item {% if request.endpoint == 'personnel' %}active{% endif %}">
                    <i class="fas fa-users"></i>
                    <span>Gestion Personnel</span>
                </a>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="main-content">
            {% block content %}{% endblock %}
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    {% block scripts %}{% endblock %}
</body>
</html>
