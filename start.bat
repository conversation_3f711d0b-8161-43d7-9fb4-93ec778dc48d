@echo off
echo.
echo ========================================
echo   Application de Gestion Industrielle
echo ========================================
echo.

REM Vérifier si Python est installé
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python n'est pas installé ou n'est pas dans le PATH
    echo Veuillez installer Python 3.8 ou supérieur depuis https://python.org
    pause
    exit /b 1
)

echo ✅ Python détecté
echo.

REM Installer les dépendances si nécessaire
echo 📦 Vérification des dépendances...
python -c "import flask, sqlalchemy" >nul 2>&1
if errorlevel 1 (
    echo Installation des dépendances...
    python -m pip install -r requirements.txt
    if errorlevel 1 (
        echo ❌ Erreur lors de l'installation des dépendances
        pause
        exit /b 1
    )
)

echo ✅ Dépendances OK
echo.

REM Vérifier la base de données
if not exist "gestion_industrielle.db" (
    echo 🗄️ Initialisation de la base de données...
    python init_db.py
    if errorlevel 1 (
        echo ❌ Erreur lors de l'initialisation de la base de données
        pause
        exit /b 1
    )
)

echo ✅ Base de données OK
echo.

REM Démarrer l'application
echo 🚀 Démarrage de l'application...
echo 📱 Application accessible à: http://127.0.0.1:5000
echo 🔄 Mode debug activé
echo ⏹️ Appuyez sur Ctrl+C pour arrêter
echo.
echo ========================================
echo.

REM Ouvrir le navigateur après 3 secondes
start "" cmd /c "timeout /t 3 /nobreak >nul && start http://127.0.0.1:5000"

REM Démarrer Flask
python app.py

echo.
echo 👋 Application arrêtée
pause
