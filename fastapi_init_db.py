"""
Script d'initialisation de la base de données pour FastAPI
"""

from fastapi_database import SessionLocal, reset_database
from fastapi_models import *
from datetime import datetime, date, time, timedelta
import random

def create_sample_data():
    """Créer des données d'exemple pour tester l'application"""
    
    db = SessionLocal()
    
    try:
        # Équipements
        equipments = [
            Equipment(name="Presse Hydraulique HP-201", code="HP-201", type="Presse", location="Atelier A", 
                     installation_date=date(2020, 1, 15), last_maintenance=date(2024, 1, 10), 
                     next_maintenance=date(2024, 4, 10)),
            Equipment(name="Tour CNC TC-105", code="TC-105", type="Tour", location="Atelier B", 
                     installation_date=date(2019, 6, 20), last_maintenance=date(2024, 1, 5), 
                     next_maintenance=date(2024, 3, 5)),
            Equipment(name="Fraiseuse FM-302", code="FM-302", type="Fraiseuse", location="Atelier C", 
                     installation_date=date(2021, 3, 10), last_maintenance=date(2024, 1, 15), 
                     next_maintenance=date(2024, 4, 15)),
            Equipment(name="Convoyeur CV-450", code="CV-450", type="Convoyeur", location="Ligne 1", 
                     installation_date=date(2018, 11, 5), status="En panne"),
            Equipment(name="Robot Soudage RS-220", code="RS-220", type="Robot", location="Atelier D", 
                     installation_date=date(2022, 8, 12), last_maintenance=date(2024, 1, 20), 
                     next_maintenance=date(2024, 5, 20))
        ]
        
        for equipment in equipments:
            db.add(equipment)
        
        db.commit()
        
        # Interventions
        interventions = [
            Intervention(equipment_id=1, type="Préventive", description="Changement des joints hydrauliques", 
                        start_time=datetime(2024, 1, 10, 8, 0), end_time=datetime(2024, 1, 10, 12, 0), 
                        technician="Jean Dupont", cost=450.0, status="Terminé"),
            Intervention(equipment_id=2, type="Corrective", description="Réparation du système de refroidissement", 
                        start_time=datetime(2024, 1, 5, 14, 0), end_time=datetime(2024, 1, 5, 18, 0), 
                        technician="Marie Martin", cost=320.0, status="Terminé"),
            Intervention(equipment_id=4, type="Urgente", description="Panne moteur principal", 
                        start_time=datetime(2024, 1, 25, 9, 0), technician="Pierre Durand", 
                        cost=1200.0, status="En cours"),
            Intervention(equipment_id=3, type="Préventive", description="Lubrification et contrôle général", 
                        start_time=datetime(2024, 1, 15, 10, 0), end_time=datetime(2024, 1, 15, 11, 30), 
                        technician="Sophie Leroy", cost=150.0, status="Terminé")
        ]
        
        for intervention in interventions:
            db.add(intervention)
        
        # Pièces de rechange
        spare_parts = [
            SparePart(equipment_id=1, name="Joint hydraulique", reference="JH-HP201-01", 
                     quantity_stock=15, quantity_min=5, unit_price=25.0, supplier="Hydraulic Parts SA"),
            SparePart(equipment_id=1, name="Filtre hydraulique", reference="FH-HP201-02", 
                     quantity_stock=8, quantity_min=3, unit_price=45.0, supplier="Hydraulic Parts SA"),
            SparePart(equipment_id=2, name="Plaquette de coupe", reference="PC-TC105-01", 
                     quantity_stock=50, quantity_min=20, unit_price=12.0, supplier="Cutting Tools Ltd"),
            SparePart(equipment_id=2, name="Liquide de refroidissement", reference="LR-TC105-02", 
                     quantity_stock=2, quantity_min=5, unit_price=35.0, supplier="Coolant Pro"),
            SparePart(equipment_id=4, name="Moteur électrique", reference="ME-CV450-01", 
                     quantity_stock=1, quantity_min=2, unit_price=850.0, supplier="Electric Motors Inc"),
            SparePart(equipment_id=5, name="Électrode de soudage", reference="ES-RS220-01", 
                     quantity_stock=100, quantity_min=50, unit_price=3.5, supplier="Welding Supply Co")
        ]
        
        for part in spare_parts:
            db.add(part)
        
        # Stock
        stocks = [
            Stock(product_name="Acier inoxydable 304", product_code="AI-304", category="Matière première", 
                  quantity_current=500, quantity_min=100, quantity_max=1000, unit="kg", location="Entrepôt A"),
            Stock(product_name="Aluminium 6061", product_code="AL-6061", category="Matière première", 
                  quantity_current=300, quantity_min=50, quantity_max=800, unit="kg", location="Entrepôt A"),
            Stock(product_name="Pièce usinée Type A", product_code="PU-A001", category="Produit fini", 
                  quantity_current=150, quantity_min=30, quantity_max=500, unit="pcs", location="Entrepôt B"),
            Stock(product_name="Pièce usinée Type B", product_code="PU-B001", category="Produit fini", 
                  quantity_current=80, quantity_min=20, quantity_max=300, unit="pcs", location="Entrepôt B"),
            Stock(product_name="Ébauche forgée", product_code="EF-001", category="Semi-fini", 
                  quantity_current=200, quantity_min=40, quantity_max=600, unit="pcs", location="Entrepôt C")
        ]
        
        for stock in stocks:
            db.add(stock)
        
        # Production
        productions = []
        for i in range(30):  # 30 jours de production
            production_date = date.today() - timedelta(days=i)
            for line in ["Ligne 1", "Ligne 2", "Ligne 3"]:
                for shift in ["Matin", "Après-midi", "Nuit"]:
                    quantity_target = random.randint(80, 120)
                    quantity_produced = random.randint(int(quantity_target * 0.7), quantity_target)
                    quality_rate = random.uniform(85, 99)
                    downtime = random.randint(0, 60)
                    
                    production = Production(
                        date=production_date, line=line, shift=shift,
                        product_type="Pièce usinée Type A" if random.random() > 0.5 else "Pièce usinée Type B",
                        quantity_produced=quantity_produced, quantity_target=quantity_target,
                        quality_rate=quality_rate, downtime_minutes=downtime
                    )
                    productions.append(production)
        
        for production in productions:
            db.add(production)
        
        # Employés
        employees = [
            Employee(employee_id="EMP001", first_name="Jean", last_name="Dupont", 
                    position="Technicien Maintenance", department="Maintenance", 
                    hire_date=date(2018, 3, 15), salary=35000),
            Employee(employee_id="EMP002", first_name="Marie", last_name="Martin", 
                    position="Technicienne Maintenance", department="Maintenance", 
                    hire_date=date(2019, 7, 20), salary=37000),
            Employee(employee_id="EMP003", first_name="Pierre", last_name="Durand", 
                    position="Chef d'équipe Maintenance", department="Maintenance", 
                    hire_date=date(2015, 1, 10), salary=45000),
            Employee(employee_id="EMP004", first_name="Sophie", last_name="Leroy", 
                    position="Opératrice Production", department="Production", 
                    hire_date=date(2020, 9, 5), salary=32000),
            Employee(employee_id="EMP005", first_name="Michel", last_name="Bernard", 
                    position="Responsable Production", department="Production", 
                    hire_date=date(2016, 4, 12), salary=50000),
            Employee(employee_id="EMP006", first_name="Claire", last_name="Moreau", 
                    position="Contrôleuse Qualité", department="Qualité", 
                    hire_date=date(2021, 2, 28), salary=38000),
            Employee(employee_id="EMP007", first_name="Thomas", last_name="Petit", 
                    position="Opérateur Production", department="Production", 
                    hire_date=date(2022, 6, 15), salary=30000),
            Employee(employee_id="EMP008", first_name="Isabelle", last_name="Roux", 
                    position="Assistante RH", department="Ressources Humaines", 
                    hire_date=date(2017, 11, 3), salary=33000)
        ]
        
        for employee in employees:
            db.add(employee)
        
        db.commit()
        
        # Pointages (derniers 15 jours)
        for i in range(15):
            record_date = date.today() - timedelta(days=i)
            for emp_id in range(1, 9):  # Pour chaque employé
                if random.random() > 0.05:  # 95% de présence
                    check_in_hour = random.randint(7, 9)
                    check_in_minute = random.randint(0, 59)
                    check_out_hour = random.randint(16, 18)
                    check_out_minute = random.randint(0, 59)
                    
                    status = "Présent"
                    if check_in_hour > 8 or (check_in_hour == 8 and check_in_minute > 30):
                        status = "Retard"
                    
                    overtime = max(0, (check_out_hour - 17) + (check_out_minute / 60))
                    
                    time_record = TimeRecord(
                        employee_id=emp_id, date=record_date,
                        check_in=time(check_in_hour, check_in_minute),
                        check_out=time(check_out_hour, check_out_minute),
                        break_duration=random.randint(30, 60),
                        overtime_hours=overtime, status=status
                    )
                    db.add(time_record)
                else:
                    # Absent
                    time_record = TimeRecord(
                        employee_id=emp_id, date=record_date, status="Absent"
                    )
                    db.add(time_record)
        
        # Congés
        leaves = [
            Leave(employee_id=1, type="Congé payé", start_date=date(2024, 2, 5), 
                  end_date=date(2024, 2, 9), days_count=5, status="Approuvé", 
                  reason="Vacances familiales"),
            Leave(employee_id=3, type="Formation", start_date=date(2024, 2, 12), 
                  end_date=date(2024, 2, 14), days_count=3, status="Approuvé", 
                  reason="Formation sécurité"),
            Leave(employee_id=5, type="Maladie", start_date=date(2024, 1, 22), 
                  end_date=date(2024, 1, 24), days_count=3, status="Approuvé", 
                  reason="Grippe"),
            Leave(employee_id=7, type="Congé payé", start_date=date(2024, 3, 1), 
                  end_date=date(2024, 3, 8), days_count=6, status="En attente", 
                  reason="Congés annuels")
        ]
        
        for leave in leaves:
            db.add(leave)
        
        db.commit()
        print("✅ Données d'exemple créées avec succès!")
        
    except Exception as e:
        db.rollback()
        print(f"❌ Erreur lors de la création des données: {e}")
    finally:
        db.close()

if __name__ == "__main__":
    print("🗄️ Réinitialisation de la base de données...")
    reset_database()
    
    print("📊 Création des données d'exemple...")
    create_sample_data()
    
    print("🎉 Base de données FastAPI initialisée avec succès!")
