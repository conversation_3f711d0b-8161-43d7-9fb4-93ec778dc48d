"""
Test simple pour diagnostiquer les problèmes FastAPI
"""

from fastapi_database import SessionLocal
from fastapi_models import *
from sqlalchemy import func
from datetime import date

def test_database():
    """Tester la base de données"""
    db = SessionLocal()
    
    try:
        # Test simple
        print("🔍 Test de la base de données...")
        
        # Compter les équipements
        equipment_count = db.query(Equipment).count()
        print(f"✅ Nombre d'équipements: {equipment_count}")
        
        # Compter les productions
        production_count = db.query(Production).count()
        print(f"✅ Nombre de productions: {production_count}")
        
        # Test de la requête problématique
        today = date.today()
        print(f"📅 Date d'aujourd'hui: {today}")
        
        # Requête simple d'abord
        productions_today = db.query(Production).filter(Production.date == today).all()
        print(f"📊 Productions aujourd'hui: {len(productions_today)}")
        
        # Maintenant la somme
        if productions_today:
            total_produced = sum(p.quantity_produced for p in productions_today)
            print(f"🏭 Total produit aujourd'hui: {total_produced}")
        else:
            print("⚠️ Aucune production aujourd'hui")
            
        # Test avec func.sum
        try:
            daily_production_result = db.query(func.sum(Production.quantity_produced)).filter(
                Production.date == today
            ).scalar()
            print(f"🔢 Résultat func.sum: {daily_production_result}")
        except Exception as e:
            print(f"❌ Erreur func.sum: {e}")
            
        print("✅ Test de base de données terminé")
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
    finally:
        db.close()

if __name__ == "__main__":
    test_database()
