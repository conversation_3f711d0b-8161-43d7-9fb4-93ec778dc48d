{% extends "fastapi_base.html" %}

{% block title %}Gestion Production FastAPI - {{ super() }}{% endblock %}

{% block content %}
<div class="header">
    <h2>
        <i class="fas fa-cogs"></i> 
        Gestion de Production
        <span class="fastapi-badge">FastAPI</span>
    </h2>
    <div class="user-info">
        <img src="https://ui-avatars.com/api/?name=Production&background=2ecc71&color=fff" alt="User">
        <span>Responsable Production</span>
    </div>
</div>

<!-- Onglets de navigation -->
<div class="tabs-container">
    <div class="tabs">
        <button class="tab-button active" data-tab="daily-production">
            <i class="fas fa-chart-line"></i> Production Journalière
        </button>
        <button class="tab-button" data-tab="stock-management">
            <i class="fas fa-warehouse"></i> Gestion de Stock
        </button>
    </div>
</div>

<!-- Contenu des onglets -->
<div class="tab-content">
    <!-- Onglet Production Journalière -->
    <div id="daily-production" class="tab-pane active">
        <div class="section-header">
            <h3><i class="fas fa-chart-line"></i> Production des 7 Derniers Jours</h3>
            <div class="timestamp"></div>
        </div>
        <div class="data-table">
            <div id="production-table" class="loading">Chargement de la production...</div>
        </div>
    </div>

    <!-- Onglet Gestion de Stock -->
    <div id="stock-management" class="tab-pane">
        <div class="section-header">
            <h3><i class="fas fa-warehouse"></i> Niveaux de Stock</h3>
            <div class="timestamp"></div>
        </div>
        <div class="data-table">
            <div id="stock-table" class="loading">Chargement des stocks...</div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Gestion des onglets
    const tabButtons = document.querySelectorAll('.tab-button');
    const tabPanes = document.querySelectorAll('.tab-pane');

    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            const tabId = button.dataset.tab;
            
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabPanes.forEach(pane => pane.classList.remove('active'));
            
            button.classList.add('active');
            document.getElementById(tabId).classList.add('active');
            
            loadTabData(tabId);
        });
    });

    loadProductionData();
});

function loadProductionData() {
    loadDailyProduction();
    loadStocks();
}

function loadTabData(tabId) {
    switch(tabId) {
        case 'daily-production':
            loadDailyProduction();
            break;
        case 'stock-management':
            loadStocks();
            break;
    }
}

function loadDailyProduction() {
    window.fastAPIRequest('/production/daily')
        .then(data => {
            const columns = [
                { field: 'date', title: 'Date', render: (value) => window.industrialApp.formatDate(value) },
                { field: 'line', title: 'Ligne' },
                { field: 'shift', title: 'Équipe' },
                { field: 'product_type', title: 'Produit' },
                { field: 'quantity_produced', title: 'Produit', render: (value) => window.industrialApp.formatNumber(value) },
                { field: 'quantity_target', title: 'Objectif', render: (value) => window.industrialApp.formatNumber(value) },
                { 
                    field: 'quantity_produced', 
                    title: 'Efficacité (%)',
                    render: (value, row) => {
                        const efficiency = Math.round((value / row.quantity_target) * 100);
                        const color = efficiency >= 90 ? '#2ecc71' : efficiency >= 75 ? '#f39c12' : '#e74c3c';
                        return `<span style="color: ${color}">${efficiency}%</span>`;
                    }
                },
                { 
                    field: 'quality_rate', 
                    title: 'Qualité (%)',
                    render: (value) => {
                        const color = value >= 95 ? '#2ecc71' : value >= 90 ? '#f39c12' : '#e74c3c';
                        return `<span style="color: ${color}">${Math.round(value)}%</span>`;
                    }
                },
                { 
                    field: 'downtime_minutes', 
                    title: 'Arrêt (min)',
                    render: (value) => {
                        const color = value <= 30 ? '#2ecc71' : value <= 60 ? '#f39c12' : '#e74c3c';
                        return `<span style="color: ${color}">${value}</span>`;
                    }
                }
            ];
            
            window.industrialApp.createDataTable('production-table', data, columns);
        })
        .catch(error => {
            console.error('Erreur lors du chargement de la production:', error);
            document.getElementById('production-table').innerHTML = '<div class="error">Erreur de chargement</div>';
        });
}

function loadStocks() {
    window.fastAPIRequest('/production/stocks')
        .then(data => {
            const columns = [
                { field: 'product_code', title: 'Code' },
                { field: 'product_name', title: 'Produit' },
                { field: 'category', title: 'Catégorie' },
                { 
                    field: 'quantity_current', 
                    title: 'Stock Actuel',
                    render: (value, row) => {
                        const color = value <= row.quantity_min ? '#e74c3c' : 
                                     value <= row.quantity_min * 2 ? '#f39c12' : '#2ecc71';
                        return `<span style="color: ${color}">${value} ${row.unit}</span>`;
                    }
                },
                { field: 'quantity_min', title: 'Stock Min', render: (value, row) => `${value} ${row.unit}` },
                { field: 'quantity_max', title: 'Stock Max', render: (value, row) => `${value} ${row.unit}` },
                { 
                    field: 'quantity_current', 
                    title: 'Taux (%)',
                    render: (value, row) => {
                        const rate = Math.round((value / row.quantity_max) * 100);
                        const color = rate >= 70 ? '#2ecc71' : rate >= 40 ? '#f39c12' : '#e74c3c';
                        return `<span style="color: ${color}">${rate}%</span>`;
                    }
                },
                { field: 'location', title: 'Localisation' }
            ];
            
            window.industrialApp.createDataTable('stock-table', data, columns);
        })
        .catch(error => {
            console.error('Erreur lors du chargement des stocks:', error);
            document.getElementById('stock-table').innerHTML = '<div class="error">Erreur de chargement</div>';
        });
}
</script>
{% endblock %}
