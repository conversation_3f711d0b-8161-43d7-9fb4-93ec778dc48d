{% extends "fastapi_base.html" %}

{% block title %}Documentation FastAPI - {{ super() }}{% endblock %}

{% block content %}
<div class="header">
    <h2>
        <i class="fas fa-question-circle"></i> 
        Documentation FastAPI
        <span class="fastapi-badge">FastAPI</span>
    </h2>
    <div class="user-info">
        <img src="https://ui-avatars.com/api/?name=FastAPI&background=009688&color=fff" alt="Help">
        <span>Guide FastAPI</span>
    </div>
</div>

<div class="help-content">
    <div class="help-section">
        <h3><i class="fas fa-rocket"></i> Application FastAPI</h3>
        <p>Cette application de gestion industrielle est maintenant propulsée par <strong>FastAPI</strong>, 
        un framework web moderne et haute performance pour Python.</p>
        
        <h4>🚀 Avantages de FastAPI</h4>
        <ul>
            <li><strong>Performance exceptionnelle</strong> : Comparable à NodeJS et Go</li>
            <li><strong>Documentation automatique</strong> : Swagger UI et ReDoc intégrés</li>
            <li><strong>Validation automatique</strong> : Avec Pydantic pour la robustesse</li>
            <li><strong>Type hints natifs</strong> : Meilleure expérience de développement</li>
            <li><strong>Standards modernes</strong> : OpenAPI, JSON Schema</li>
        </ul>
    </div>

    <div class="help-section">
        <h3><i class="fas fa-book"></i> Documentation API Interactive</h3>
        <p>FastAPI génère automatiquement une documentation interactive pour toutes les APIs :</p>
        
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0;">
            <a href="/api/docs" target="_blank" class="btn btn-primary" style="padding: 20px; text-align: center; text-decoration: none;">
                <i class="fas fa-code" style="font-size: 2rem; display: block; margin-bottom: 10px;"></i>
                <strong>Swagger UI</strong><br>
                Documentation interactive
            </a>
            <a href="/api/redoc" target="_blank" class="btn btn-secondary" style="padding: 20px; text-align: center; text-decoration: none;">
                <i class="fas fa-file-alt" style="font-size: 2rem; display: block; margin-bottom: 10px;"></i>
                <strong>ReDoc</strong><br>
                Documentation élégante
            </a>
        </div>
    </div>

    <div class="help-section">
        <h3><i class="fas fa-tachometer-alt"></i> Tableau de Bord FastAPI</h3>
        <p>Le tableau de bord utilise les APIs FastAPI pour afficher les données en temps réel :</p>
        <ul>
            <li><strong>KPIs en temps réel</strong> : Taux de panne, production, absentéisme, stock</li>
            <li><strong>Graphiques interactifs</strong> : Performance des modules et tendances</li>
            <li><strong>Alertes automatiques</strong> : Notifications basées sur les seuils</li>
            <li><strong>Mise à jour automatique</strong> : Données rafraîchies régulièrement</li>
        </ul>
    </div>

    <div class="help-section">
        <h3><i class="fas fa-tools"></i> Module Maintenance FastAPI</h3>
        <p>Gestion complète des équipements avec APIs REST :</p>
        
        <h4>Équipements</h4>
        <ul>
            <li><strong>CRUD complet</strong> : Créer, lire, modifier, supprimer</li>
            <li><strong>Validation Pydantic</strong> : Données validées automatiquement</li>
            <li><strong>Gestion des erreurs</strong> : Messages d'erreur clairs</li>
        </ul>

        <h4>APIs Disponibles</h4>
        <ul>
            <li><code>GET /api/maintenance/equipments</code> - Liste des équipements</li>
            <li><code>POST /api/maintenance/equipments</code> - Créer un équipement</li>
            <li><code>PUT /api/maintenance/equipments/{id}</code> - Modifier un équipement</li>
            <li><code>DELETE /api/maintenance/equipments/{id}</code> - Supprimer un équipement</li>
        </ul>
    </div>

    <div class="help-section">
        <h3><i class="fas fa-cogs"></i> Module Production FastAPI</h3>
        <p>Suivi de la production avec APIs optimisées :</p>
        <ul>
            <li><strong>Production journalière</strong> : Données des 7 derniers jours</li>
            <li><strong>Gestion de stock</strong> : Niveaux et alertes en temps réel</li>
            <li><strong>Calculs automatiques</strong> : Efficacité, taux de qualité</li>
        </ul>
    </div>

    <div class="help-section">
        <h3><i class="fas fa-users"></i> Module Personnel FastAPI</h3>
        <p>Gestion RH avec validation robuste :</p>
        <ul>
            <li><strong>Employés actifs</strong> : Liste complète avec détails</li>
            <li><strong>Pointage</strong> : Suivi des présences et heures</li>
            <li><strong>Congés</strong> : Gestion des demandes et approbations</li>
        </ul>
    </div>

    <div class="help-section">
        <h3><i class="fas fa-code"></i> Utilisation des APIs</h3>
        <p>Exemples d'utilisation des APIs FastAPI :</p>
        
        <h4>Récupérer les KPIs</h4>
        <pre style="background: rgba(0,0,0,0.2); padding: 15px; border-radius: 5px; overflow-x: auto;">
<code>curl -X GET "http://127.0.0.1:8000/api/dashboard/kpis" \
     -H "accept: application/json"</code></pre>

        <h4>Créer un équipement</h4>
        <pre style="background: rgba(0,0,0,0.2); padding: 15px; border-radius: 5px; overflow-x: auto;">
<code>curl -X POST "http://127.0.0.1:8000/api/maintenance/equipments" \
     -H "accept: application/json" \
     -H "Content-Type: application/json" \
     -d '{
       "name": "Nouveau Équipement",
       "code": "NE-001",
       "type": "Machine",
       "location": "Atelier A",
       "installation_date": "2024-01-01"
     }'</code></pre>
    </div>

    <div class="help-section">
        <h3><i class="fas fa-shield-alt"></i> Validation et Sécurité</h3>
        <ul>
            <li><strong>Validation Pydantic</strong> : Tous les données sont validées automatiquement</li>
            <li><strong>Type safety</strong> : Types Python stricts pour éviter les erreurs</li>
            <li><strong>Gestion d'erreurs</strong> : Messages d'erreur détaillés et structurés</li>
            <li><strong>Standards OpenAPI</strong> : APIs conformes aux standards</li>
        </ul>
    </div>

    <div class="help-section">
        <h3><i class="fas fa-rocket"></i> Performance</h3>
        <p>FastAPI offre des performances exceptionnelles :</p>
        <ul>
            <li><strong>Vitesse</strong> : Parmi les frameworks Python les plus rapides</li>
            <li><strong>Async/Await</strong> : Support natif de la programmation asynchrone</li>
            <li><strong>Optimisations</strong> : Basé sur Starlette et Pydantic optimisés</li>
            <li><strong>Scalabilité</strong> : Capable de gérer de nombreuses requêtes simultanées</li>
        </ul>
    </div>

    <div class="help-section">
        <h3><i class="fas fa-question-circle"></i> Support et Ressources</h3>
        <ul>
            <li><strong>Documentation officielle</strong> : <a href="https://fastapi.tiangolo.com/" target="_blank">fastapi.tiangolo.com</a></li>
            <li><strong>Code source</strong> : <a href="https://github.com/tiangolo/fastapi" target="_blank">GitHub FastAPI</a></li>
            <li><strong>Communauté</strong> : Discord, Stack Overflow, GitHub Discussions</li>
        </ul>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Smooth scroll pour les liens internes
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
    
    // Notification de bienvenue FastAPI
    setTimeout(() => {
        window.industrialApp.showNotification(
            '📚 Consultez la documentation API interactive dans les liens ci-dessus!',
            'info',
            5000
        );
    }, 2000);
});
</script>
{% endblock %}
