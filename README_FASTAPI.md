# 🚀 Application de Gestion Industrielle - FastAPI

Une application web moderne et haute performance pour la gestion industrielle, développée avec **FastAPI**, **SQLAlchemy 2.0**, et **Pydantic**.

## ✨ Nouveautés FastAPI

### 🚀 **Performance Exceptionnelle**
- **Vitesse** : Comparable à NodeJS et Go
- **Async/Await** : Support natif de la programmation asynchrone
- **Optimisations** : Basé sur Starlette et Pydantic

### 📚 **Documentation Automatique**
- **Swagger UI** : Documentation interactive à `/api/docs`
- **ReDoc** : Documentation élégante à `/api/redoc`
- **OpenAPI** : Standards modernes intégrés

### ✅ **Validation Robuste**
- **Pydantic** : Validation automatique des données
- **Type Hints** : Types Python stricts
- **Gestion d'erreurs** : Messages détaillés et structurés

## 🛠️ Technologies

- **Backend** : FastAPI 0.104.0, Uvicorn
- **ORM** : SQLAlchemy 2.0.41
- **Validation** : Pydantic 2.5.0
- **Base de données** : SQLite
- **Frontend** : HTML5, CSS3, JavaScript ES6+
- **Graphiques** : Chart.js
- **Templates** : Jinja2

## 🚀 Démarrage Rapide

### Option 1 : Script automatique (Windows)
```bash
start_fastapi.bat
```

### Option 2 : Script Python (multiplateforme)
```bash
python start_fastapi.py
```

### Option 3 : Manuel
```bash
# Installer les dépendances
pip install -r requirements.txt

# Initialiser la base de données
python fastapi_init_db.py

# Démarrer l'application
python -m uvicorn fastapi_app:app --host 0.0.0.0 --port 8000 --reload
```

## 🌐 Accès à l'Application

- **Application web** : http://127.0.0.1:8000
- **Documentation Swagger** : http://127.0.0.1:8000/api/docs
- **Documentation ReDoc** : http://127.0.0.1:8000/api/redoc

## 📊 APIs Disponibles

### Dashboard
- `GET /api/dashboard/kpis` - KPIs principaux
- `GET /api/dashboard/performance` - Performance des modules
- `GET /api/dashboard/alerts` - Alertes en temps réel
- `GET /api/dashboard/production-trend` - Tendance production 7j

### Maintenance (CRUD complet)
- `GET /api/maintenance/equipments` - Liste équipements
- `POST /api/maintenance/equipments` - Créer équipement
- `GET /api/maintenance/equipments/{id}` - Détails équipement
- `PUT /api/maintenance/equipments/{id}` - Modifier équipement
- `DELETE /api/maintenance/equipments/{id}` - Supprimer équipement
- `GET /api/maintenance/interventions` - Liste interventions
- `POST /api/maintenance/interventions` - Créer intervention
- `GET /api/maintenance/spare-parts` - Pièces de rechange

### Production
- `GET /api/production/daily` - Production journalière
- `GET /api/production/stocks` - Niveaux de stock

### Personnel
- `GET /api/personnel/employees` - Liste employés
- `POST /api/personnel/employees` - Créer employé
- `GET /api/personnel/attendance` - Données pointage
- `GET /api/personnel/leaves` - Gestion congés

## 🔧 Exemples d'Utilisation

### Récupérer les KPIs
```bash
curl -X GET "http://127.0.0.1:8000/api/dashboard/kpis" \
     -H "accept: application/json"
```

### Créer un équipement
```bash
curl -X POST "http://127.0.0.1:8000/api/maintenance/equipments" \
     -H "accept: application/json" \
     -H "Content-Type: application/json" \
     -d '{
       "name": "Nouveau Équipement",
       "code": "NE-001",
       "type": "Machine",
       "location": "Atelier A",
       "installation_date": "2024-01-01",
       "status": "Opérationnel"
     }'
```

### Modifier un équipement
```bash
curl -X PUT "http://127.0.0.1:8000/api/maintenance/equipments/1" \
     -H "accept: application/json" \
     -H "Content-Type: application/json" \
     -d '{
       "name": "Équipement Modifié",
       "status": "Maintenance"
     }'
```

## 📋 Schémas Pydantic

L'application utilise des schémas Pydantic pour la validation :

### Équipement
```python
class EquipmentCreate(BaseModel):
    name: str = Field(..., min_length=1, max_length=100)
    code: str = Field(..., min_length=1, max_length=50)
    type: str = Field(..., min_length=1, max_length=50)
    location: str = Field(..., min_length=1, max_length=100)
    status: EquipmentStatus = EquipmentStatus.OPERATIONAL
    installation_date: date
    last_maintenance: Optional[date] = None
    next_maintenance: Optional[date] = None
```

### Intervention
```python
class InterventionCreate(BaseModel):
    equipment_id: int
    type: InterventionType
    description: str = Field(..., min_length=1)
    start_time: datetime
    end_time: Optional[datetime] = None
    technician: str = Field(..., min_length=1, max_length=100)
    cost: float = Field(default=0.0, ge=0)
    status: InterventionStatus = InterventionStatus.IN_PROGRESS
```

## 🎯 Fonctionnalités Clés

### ✅ Implémentées
- ✅ **APIs REST complètes** avec validation Pydantic
- ✅ **Documentation automatique** Swagger UI + ReDoc
- ✅ **CRUD complet** pour équipements avec gestion d'erreurs
- ✅ **Dashboard temps réel** avec KPIs et graphiques
- ✅ **Validation robuste** des données d'entrée
- ✅ **Gestion d'erreurs** structurée avec messages clairs
- ✅ **Performance optimisée** avec FastAPI + Uvicorn
- ✅ **Interface moderne** avec thème FastAPI
- ✅ **Base de données** SQLite avec données d'exemple

### 🔄 Améliorations Possibles
- 🔄 **Authentification JWT** avec FastAPI Security
- 🔄 **Tests automatisés** avec pytest et TestClient
- 🔄 **Middleware** pour logging et monitoring
- 🔄 **WebSockets** pour mises à jour temps réel
- 🔄 **Background tasks** pour tâches asynchrones
- 🔄 **Containerisation** avec Docker
- 🔄 **Déploiement** avec Gunicorn/Nginx

## 🔒 Validation et Sécurité

### Validation Pydantic
- **Types stricts** : Validation automatique des types
- **Contraintes** : min_length, max_length, ge, le, etc.
- **Enums** : Valeurs prédéfinies pour les statuts
- **Dates** : Validation automatique des formats de date

### Gestion d'erreurs
- **HTTPException** : Codes d'erreur HTTP appropriés
- **Messages détaillés** : Informations précises sur les erreurs
- **Rollback automatique** : Annulation des transactions en cas d'erreur

## 📈 Performance

### Benchmarks FastAPI
- **Requêtes/seconde** : Jusqu'à 60,000+ req/s
- **Latence** : < 1ms pour les requêtes simples
- **Mémoire** : Utilisation optimisée
- **CPU** : Efficacité maximale avec async/await

### Optimisations
- **Connection pooling** : SQLAlchemy optimisé
- **Lazy loading** : Chargement à la demande
- **Pagination** : Limitation des résultats
- **Caching** : Mise en cache possible

## 🧪 Tests

### Lancer les tests
```bash
# Tests de base de données
python test_fastapi.py

# Tests d'API (à implémenter)
pytest tests/
```

## 📁 Structure du Projet FastAPI

```
📁 Aymen projet/
├── 📄 fastapi_app.py              # Application FastAPI principale
├── 📄 fastapi_models.py           # Modèles SQLAlchemy
├── 📄 fastapi_database.py         # Configuration base de données
├── 📄 fastapi_init_db.py          # Initialisation avec données
├── 📄 schemas.py                  # Schémas Pydantic
├── 📄 start_fastapi.py            # Script démarrage Python
├── 📄 start_fastapi.bat           # Script démarrage Windows
├── 📄 test_fastapi.py             # Tests de diagnostic
├── 📄 README_FASTAPI.md           # Cette documentation
├── 📄 requirements.txt            # Dépendances FastAPI
│
├── 📁 templates/                  # Templates FastAPI
│   ├── 📄 fastapi_base.html      # Template de base
│   ├── 📄 fastapi_dashboard.html # Dashboard FastAPI
│   ├── 📄 fastapi_maintenance.html # Module maintenance
│   ├── 📄 fastapi_production.html # Module production
│   ├── 📄 fastapi_personnel.html # Module personnel
│   └── 📄 fastapi_help.html      # Documentation
│
└── 📁 static/                     # Fichiers statiques (partagés)
    ├── 📁 css/style.css
    └── 📁 js/main.js
```

## 🌟 Avantages FastAPI vs Flask

| Aspect | FastAPI | Flask |
|--------|---------|-------|
| **Performance** | ⚡ Très élevée | 🐌 Modérée |
| **Documentation** | 📚 Automatique | 📝 Manuelle |
| **Validation** | ✅ Intégrée | 🔧 Extensions |
| **Type Hints** | 🎯 Natif | 🔧 Optionnel |
| **Async/Await** | 🚀 Natif | 🔧 Extensions |
| **Standards** | 📋 OpenAPI | 🔧 Personnalisé |

## 📞 Support

- **Documentation FastAPI** : https://fastapi.tiangolo.com/
- **GitHub FastAPI** : https://github.com/tiangolo/fastapi
- **Communauté** : Discord, Stack Overflow

---

**Version FastAPI** : 2.0.0  
**Dernière mise à jour** : Juillet 2025  
**Performance** : 🚀 Optimisée pour la production
