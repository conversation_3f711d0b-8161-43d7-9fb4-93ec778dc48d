from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
from sqlalchemy import func

db = SQLAlchemy()

class Equipment(db.Model):
    """Modèle pour les équipements de maintenance"""
    __tablename__ = 'equipments'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    code = db.Column(db.String(50), unique=True, nullable=False)
    type = db.Column(db.String(50), nullable=False)
    location = db.Column(db.String(100), nullable=False)
    status = db.Column(db.String(20), default='Opérationnel')
    installation_date = db.Column(db.Date, nullable=False)
    last_maintenance = db.Column(db.Date)
    next_maintenance = db.Column(db.Date)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relations
    interventions = db.relationship('Intervention', backref='equipment', lazy=True)
    spare_parts = db.relationship('SparePart', backref='equipment', lazy=True)

class Intervention(db.Model):
    """Modèle pour les interventions de maintenance"""
    __tablename__ = 'interventions'
    
    id = db.Column(db.Integer, primary_key=True)
    equipment_id = db.Column(db.Integer, db.ForeignKey('equipments.id'), nullable=False)
    type = db.Column(db.String(50), nullable=False)  # Préventive, Corrective, Urgente
    description = db.Column(db.Text, nullable=False)
    start_time = db.Column(db.DateTime, nullable=False)
    end_time = db.Column(db.DateTime)
    technician = db.Column(db.String(100), nullable=False)
    cost = db.Column(db.Float, default=0.0)
    status = db.Column(db.String(20), default='En cours')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class SparePart(db.Model):
    """Modèle pour les pièces de rechange"""
    __tablename__ = 'spare_parts'
    
    id = db.Column(db.Integer, primary_key=True)
    equipment_id = db.Column(db.Integer, db.ForeignKey('equipments.id'), nullable=False)
    name = db.Column(db.String(100), nullable=False)
    reference = db.Column(db.String(50), unique=True, nullable=False)
    quantity_stock = db.Column(db.Integer, default=0)
    quantity_min = db.Column(db.Integer, default=5)
    unit_price = db.Column(db.Float, nullable=False)
    supplier = db.Column(db.String(100))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class Production(db.Model):
    """Modèle pour la production journalière"""
    __tablename__ = 'productions'
    
    id = db.Column(db.Integer, primary_key=True)
    date = db.Column(db.Date, nullable=False)
    line = db.Column(db.String(50), nullable=False)
    shift = db.Column(db.String(20), nullable=False)  # Matin, Après-midi, Nuit
    product_type = db.Column(db.String(100), nullable=False)
    quantity_produced = db.Column(db.Integer, nullable=False)
    quantity_target = db.Column(db.Integer, nullable=False)
    quality_rate = db.Column(db.Float, default=100.0)
    downtime_minutes = db.Column(db.Integer, default=0)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class Stock(db.Model):
    """Modèle pour la gestion de stock"""
    __tablename__ = 'stocks'
    
    id = db.Column(db.Integer, primary_key=True)
    product_name = db.Column(db.String(100), nullable=False)
    product_code = db.Column(db.String(50), unique=True, nullable=False)
    category = db.Column(db.String(50), nullable=False)  # Matière première, Produit fini, Semi-fini
    quantity_current = db.Column(db.Integer, default=0)
    quantity_min = db.Column(db.Integer, default=10)
    quantity_max = db.Column(db.Integer, default=1000)
    unit = db.Column(db.String(20), default='pcs')
    location = db.Column(db.String(100))
    last_movement = db.Column(db.DateTime)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class Consumption(db.Model):
    """Modèle pour les consommations de matières premières"""
    __tablename__ = 'consumptions'
    
    id = db.Column(db.Integer, primary_key=True)
    stock_id = db.Column(db.Integer, db.ForeignKey('stocks.id'), nullable=False)
    production_id = db.Column(db.Integer, db.ForeignKey('productions.id'), nullable=False)
    quantity_consumed = db.Column(db.Integer, nullable=False)
    date = db.Column(db.Date, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relations
    stock = db.relationship('Stock', backref='consumptions')
    production = db.relationship('Production', backref='consumptions')

class Employee(db.Model):
    """Modèle pour les employés"""
    __tablename__ = 'employees'
    
    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.String(20), unique=True, nullable=False)
    first_name = db.Column(db.String(50), nullable=False)
    last_name = db.Column(db.String(50), nullable=False)
    position = db.Column(db.String(100), nullable=False)
    department = db.Column(db.String(50), nullable=False)
    hire_date = db.Column(db.Date, nullable=False)
    salary = db.Column(db.Float)
    status = db.Column(db.String(20), default='Actif')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relations
    time_records = db.relationship('TimeRecord', backref='employee', lazy=True)
    leaves = db.relationship('Leave', backref='employee', lazy=True)

class TimeRecord(db.Model):
    """Modèle pour le pointage"""
    __tablename__ = 'time_records'
    
    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employees.id'), nullable=False)
    date = db.Column(db.Date, nullable=False)
    check_in = db.Column(db.Time)
    check_out = db.Column(db.Time)
    break_duration = db.Column(db.Integer, default=0)  # en minutes
    overtime_hours = db.Column(db.Float, default=0.0)
    status = db.Column(db.String(20), default='Présent')  # Présent, Absent, Retard
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class Leave(db.Model):
    """Modèle pour les congés"""
    __tablename__ = 'leaves'
    
    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employees.id'), nullable=False)
    type = db.Column(db.String(50), nullable=False)  # Congé payé, Maladie, Formation
    start_date = db.Column(db.Date, nullable=False)
    end_date = db.Column(db.Date, nullable=False)
    days_count = db.Column(db.Integer, nullable=False)
    status = db.Column(db.String(20), default='En attente')  # En attente, Approuvé, Refusé
    reason = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
