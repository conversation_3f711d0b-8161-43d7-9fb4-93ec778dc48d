:root {
    --primary: #2c3e50;
    --secondary: #3498db;
    --success: #27ae60;
    --warning: #f39c12;
    --danger: #e74c3c;
    --light: #ecf0f1;
    --dark: #34495e;
    --maintenance: #3498db;
    --production: #2ecc71;
    --personnel: #9b59b6;
    --dashboard: #e74c3c;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
    background: linear-gradient(135deg, #1a2a3a, #2c3e50);
    color: #fff;
    min-height: 100vh;
}

.container {
    display: flex;
    min-height: 100vh;
}

/* Sidebar Styles */
.sidebar {
    width: 250px;
    background: rgba(25, 35, 45, 0.9);
    padding: 20px 0;
    box-shadow: 3px 0 15px rgba(0, 0, 0, 0.2);
    z-index: 10;
}

.logo {
    text-align: center;
    padding: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.logo h1 {
    font-size: 1.5rem;
    background: linear-gradient(to right, #3498db, #2ecc71, #9b59b6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-weight: 700;
}

.nav-links {
    padding: 20px 0;
}

.nav-item {
    padding: 12px 25px;
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    text-decoration: none;
    color: #fff;
}

.nav-item:hover {
    background: rgba(52, 152, 219, 0.2);
    color: #fff;
    text-decoration: none;
}

.nav-item.active {
    background: rgba(52, 152, 219, 0.3);
    border-left: 4px solid var(--secondary);
}

.nav-item i {
    margin-right: 15px;
    width: 20px;
    text-align: center;
}

.module-title {
    padding: 15px 25px;
    text-transform: uppercase;
    font-size: 0.8rem;
    letter-spacing: 1px;
    color: #7f8c8d;
    margin-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Main Content Styles */
.main-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header h2 {
    font-size: 1.8rem;
    font-weight: 600;
    display: flex;
    align-items: center;
}

.header h2 i {
    margin-right: 15px;
    color: var(--secondary);
}

.user-info {
    display: flex;
    align-items: center;
}

.user-info img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 10px;
}

/* KPI Cards */
.kpi-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.kpi-card {
    background: rgba(255, 255, 255, 0.08);
    border-radius: 10px;
    padding: 20px;
    display: flex;
    flex-direction: column;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    transition: transform 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.kpi-card:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.12);
}

.kpi-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.kpi-title {
    font-size: 1rem;
    color: #bdc3c7;
}

.kpi-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

.kpi-value {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 5px;
}

.kpi-trend {
    display: flex;
    align-items: center;
    font-size: 0.9rem;
}

.trend-up {
    color: var(--success);
}

.trend-down {
    color: var(--danger);
}

/* Charts */
.charts-row {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 20px;
    margin-bottom: 30px;
}

.chart-container {
    background: rgba(255, 255, 255, 0.08);
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.chart-title {
    margin-bottom: 15px;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
}

.chart-title i {
    margin-right: 10px;
    color: var(--secondary);
}

.chart {
    height: 300px;
    position: relative;
}

/* Alerts */
.alerts {
    max-height: 300px;
    overflow-y: auto;
}

.alert-item {
    padding: 10px;
    margin-bottom: 10px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 5px;
    display: flex;
    align-items: center;
    font-size: 0.9rem;
}

.alert-item i {
    margin-right: 10px;
    width: 16px;
}

.loading, .no-alerts, .error {
    text-align: center;
    padding: 20px;
    color: #bdc3c7;
    font-style: italic;
}

/* Interconnections */
.interconnections {
    margin-top: 30px;
}

.interconnections h3 {
    margin-bottom: 20px;
    display: flex;
    align-items: center;
}

.interconnections h3 i {
    margin-right: 10px;
    color: var(--warning);
}

.connections-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.connection {
    background: rgba(255, 255, 255, 0.08);
    border-radius: 10px;
    padding: 20px;
    border-left: 4px solid var(--warning);
}

.connection-title {
    font-weight: 600;
    margin-bottom: 10px;
    color: var(--warning);
}

.connection-description {
    font-size: 0.95rem;
    line-height: 1.6;
    color: #bdc3c7;
}

/* Tables */
.data-table {
    width: 100%;
    background: rgba(255, 255, 255, 0.08);
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 20px;
}

.data-table table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th,
.data-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.data-table th {
    background: rgba(255, 255, 255, 0.1);
    font-weight: 600;
    color: var(--secondary);
}

.data-table tr:hover {
    background: rgba(255, 255, 255, 0.05);
}

/* Status badges */
.status-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 500;
}

.status-normal { background: rgba(46, 204, 113, 0.2); color: #2ecc71; }
.status-warning { background: rgba(243, 156, 18, 0.2); color: #f39c12; }
.status-critical { background: rgba(231, 76, 60, 0.2); color: #e74c3c; }
.status-success { background: rgba(39, 174, 96, 0.2); color: #27ae60; }

/* Tabs */
.tabs-container {
    margin-bottom: 20px;
}

.tabs {
    display: flex;
    background: rgba(255, 255, 255, 0.08);
    border-radius: 10px;
    padding: 5px;
    gap: 5px;
}

.tab-button {
    flex: 1;
    padding: 12px 20px;
    background: transparent;
    border: none;
    color: #bdc3c7;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-size: 0.9rem;
}

.tab-button:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
}

.tab-button.active {
    background: var(--secondary);
    color: #fff;
}

.tab-content {
    min-height: 400px;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
    animation: fadeIn 0.3s ease;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.section-header h3 {
    display: flex;
    align-items: center;
    color: var(--secondary);
}

.section-header h3 i {
    margin-right: 10px;
}

.section-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

.timestamp {
    font-size: 0.8rem;
    color: #7f8c8d;
}

/* Buttons */
.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 5px;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.btn-primary {
    background: var(--secondary);
    color: white;
}

.btn-primary:hover {
    background: #2980b9;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
}

.btn-danger {
    background: var(--danger);
    color: white;
}

.btn-danger:hover {
    background: #c0392b;
}

.btn-sm {
    padding: 4px 8px;
    font-size: 0.8rem;
}

/* Modales */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-overlay.active {
    display: flex;
}

.modal {
    background: var(--primary);
    border-radius: 10px;
    padding: 0;
    max-width: 600px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    color: var(--secondary);
    display: flex;
    align-items: center;
    gap: 10px;
}

.modal-close {
    background: none;
    border: none;
    color: #bdc3c7;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.modal-close:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
}

.modal form {
    padding: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    color: #ecf0f1;
    font-weight: 500;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 5px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    font-size: 0.9rem;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--secondary);
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

.modal-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    padding-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: 20px;
}

/* Help page styles */
.help-content {
    max-width: 800px;
    margin: 0 auto;
}

.help-section {
    background: rgba(255, 255, 255, 0.08);
    border-radius: 10px;
    padding: 25px;
    margin-bottom: 20px;
    border-left: 4px solid var(--secondary);
}

.help-section h3 {
    color: var(--secondary);
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.help-section h4 {
    color: #ecf0f1;
    margin: 20px 0 10px 0;
    font-size: 1.1rem;
}

.help-section p {
    margin-bottom: 15px;
    line-height: 1.6;
    color: #bdc3c7;
}

.help-section ul {
    margin-left: 20px;
    margin-bottom: 15px;
}

.help-section li {
    margin-bottom: 8px;
    line-height: 1.5;
    color: #ecf0f1;
}

.help-section strong {
    color: var(--secondary);
}

.color-legend {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.color-item {
    display: flex;
    align-items: center;
    gap: 10px;
}

.color-box {
    width: 20px;
    height: 20px;
    border-radius: 4px;
    display: inline-block;
}

/* Animation */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Responsive */
@media (max-width: 992px) {
    .charts-row {
        grid-template-columns: 1fr;
    }

    .container {
        flex-direction: column;
    }

    .sidebar {
        width: 100%;
        padding: 10px 0;
    }

    .nav-links {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
    }

    .nav-item {
        padding: 10px 15px;
    }

    .module-title {
        display: none;
    }

    .tabs {
        flex-direction: column;
    }

    .tab-button {
        justify-content: flex-start;
    }
}

@media (max-width: 576px) {
    .header {
        flex-direction: column;
        align-items: flex-start;
    }

    .user-info {
        margin-top: 15px;
    }

    .kpi-cards {
        grid-template-columns: 1fr;
    }

    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
}
