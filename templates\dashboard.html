{% extends "base.html" %}

{% block title %}Tableau de Bord - {{ super() }}{% endblock %}

{% block content %}
<div class="header">
    <h2><i class="fas fa-tachometer-alt"></i> Tableau de Bord</h2>
    <div class="user-info">
        <img src="https://ui-avatars.com/api/?name=Admin&background=3498db&color=fff" alt="User">
        <span>Administrateur</span>
    </div>
</div>

<!-- KPI Cards -->
<div class="kpi-cards">
    <div class="kpi-card">
        <div class="kpi-header">
            <div class="kpi-title">Taux de Panne</div>
            <div class="kpi-icon" style="background: rgba(231, 76, 60, 0.2); color: #e74c3c;">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
        </div>
        <div class="kpi-value" id="breakdown-rate">--</div>
        <div class="kpi-trend trend-down">
            <i class="fas fa-arrow-down"></i>
            <span>vs mois dernier</span>
        </div>
    </div>
    
    <div class="kpi-card">
        <div class="kpi-header">
            <div class="kpi-title">Production Journalière</div>
            <div class="kpi-icon" style="background: rgba(46, 204, 113, 0.2); color: #2ecc71;">
                <i class="fas fa-chart-line"></i>
            </div>
        </div>
        <div class="kpi-value" id="daily-production">--</div>
        <div class="kpi-trend trend-up">
            <i class="fas fa-arrow-up"></i>
            <span>vs hier</span>
        </div>
    </div>
    
    <div class="kpi-card">
        <div class="kpi-header">
            <div class="kpi-title">Taux d'Absentéisme</div>
            <div class="kpi-icon" style="background: rgba(155, 89, 182, 0.2); color: #9b59b6;">
                <i class="fas fa-user-clock"></i>
            </div>
        </div>
        <div class="kpi-value" id="absenteeism-rate">--</div>
        <div class="kpi-trend trend-down">
            <i class="fas fa-arrow-down"></i>
            <span>vs mois dernier</span>
        </div>
    </div>
    
    <div class="kpi-card">
        <div class="kpi-header">
            <div class="kpi-title">Niveau Stock Moyen</div>
            <div class="kpi-icon" style="background: rgba(52, 152, 219, 0.2); color: #3498db;">
                <i class="fas fa-boxes"></i>
            </div>
        </div>
        <div class="kpi-value" id="avg-stock-level">--</div>
        <div class="kpi-trend trend-up">
            <i class="fas fa-arrow-up"></i>
            <span>vs semaine dernière</span>
        </div>
    </div>
</div>

<!-- Charts -->
<div class="charts-row">
    <div class="chart-container">
        <div class="chart-title"><i class="fas fa-chart-bar"></i> Performance des Modules</div>
        <div class="chart">
            <canvas id="performanceChart"></canvas>
        </div>
    </div>

    <div class="chart-container">
        <div class="chart-title"><i class="fas fa-bell"></i> Alertes Récentes</div>
        <div class="alerts" id="alerts-container">
            <div class="loading">Chargement des alertes...</div>
        </div>
    </div>
</div>

<!-- Graphique de tendance de production -->
<div class="chart-container" style="margin-top: 20px;">
    <div class="chart-title"><i class="fas fa-chart-line"></i> Tendance de Production (7 derniers jours)</div>
    <div class="chart">
        <canvas id="productionTrendChart"></canvas>
    </div>
</div>

<!-- Interconnections -->
<div class="interconnections">
    <h3><i class="fas fa-link"></i> Interconnexions Clés</h3>
    <div class="connections-container">
        <div class="connection">
            <div class="connection-title">Interventions ↔ Équipements</div>
            <div class="connection-description">
                Suivi des interventions par équipement avec historique complet et coûts associés.
            </div>
        </div>
        
        <div class="connection">
            <div class="connection-title">Consommations ↔ Stock</div>
            <div class="connection-description">
                Mise à jour automatique des niveaux de stock basée sur les consommations de production.
            </div>
        </div>
        
        <div class="connection">
            <div class="connection-title">Pointage → Production</div>
            <div class="connection-description">
                Analyse de productivité basée sur les heures travaillées et la production réalisée.
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Charger les KPIs
    fetch('/api/dashboard/kpis')
        .then(response => response.json())
        .then(data => {
            document.getElementById('breakdown-rate').textContent = data.breakdown_rate + '%';
            document.getElementById('daily-production').textContent = data.daily_production.toLocaleString();
            document.getElementById('absenteeism-rate').textContent = data.absenteeism_rate + '%';
            document.getElementById('avg-stock-level').textContent = data.avg_stock_level + '%';
        })
        .catch(error => console.error('Erreur lors du chargement des KPIs:', error));
    
    // Charger les données de performance
    fetch('/api/dashboard/performance')
        .then(response => response.json())
        .then(data => {
            const ctx = document.getElementById('performanceChart').getContext('2d');
            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['Maintenance', 'Production', 'Personnel'],
                    datasets: [{
                        label: 'Performance (%)',
                        data: [data.maintenance, data.production, data.personnel],
                        backgroundColor: [
                            'rgba(52, 152, 219, 0.7)',
                            'rgba(46, 204, 113, 0.7)',
                            'rgba(155, 89, 182, 0.7)'
                        ],
                        borderColor: [
                            'rgba(52, 152, 219, 1)',
                            'rgba(46, 204, 113, 1)',
                            'rgba(155, 89, 182, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            grid: { color: 'rgba(255, 255, 255, 0.1)' },
                            ticks: { color: '#ecf0f1' }
                        },
                        x: {
                            grid: { display: false },
                            ticks: { color: '#ecf0f1' }
                        }
                    },
                    plugins: { legend: { display: false } }
                }
            });
        })
        .catch(error => console.error('Erreur lors du chargement des performances:', error));

    // Charger le graphique de tendance de production
    fetch('/api/dashboard/production-trend')
        .then(response => response.json())
        .then(data => {
            const ctx = document.getElementById('productionTrendChart').getContext('2d');
            new Chart(ctx, {
                type: 'line',
                data: data,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: { color: 'rgba(255, 255, 255, 0.1)' },
                            ticks: { color: '#ecf0f1' }
                        },
                        x: {
                            grid: { color: 'rgba(255, 255, 255, 0.1)' },
                            ticks: { color: '#ecf0f1' }
                        }
                    },
                    plugins: {
                        legend: {
                            labels: { color: '#ecf0f1' }
                        }
                    }
                }
            });
        })
        .catch(error => console.error('Erreur lors du chargement de la tendance:', error));

    // Charger les alertes
    fetch('/api/dashboard/alerts')
        .then(response => response.json())
        .then(data => {
            const container = document.getElementById('alerts-container');
            if (data.length === 0) {
                container.innerHTML = '<div class="no-alerts">Aucune alerte récente</div>';
            } else {
                container.innerHTML = data.map(alert => `
                    <div class="alert-item">
                        <i class="${alert.icon}" style="color: ${alert.color};"></i>
                        ${alert.message}
                    </div>
                `).join('');
            }
        })
        .catch(error => {
            console.error('Erreur lors du chargement des alertes:', error);
            document.getElementById('alerts-container').innerHTML = '<div class="error">Erreur de chargement</div>';
        });
});
</script>
{% endblock %}
