# Structure du Projet - Application de Gestion Industrielle

```
📁 Aymen projet/
├── 📄 README.md                    # Documentation principale
├── 📄 STRUCTURE.md                 # Ce fichier - structure du projet
├── 📄 requirements.txt             # Dépendances Python
├── 📄 config.py                    # Configuration de l'application
├── 📄 app.py                       # Application Flask principale
├── 📄 models.py                    # Modèles de base de données SQLAlchemy
├── 📄 database.py                  # Configuration base de données
├── 📄 init_db.py                   # Script d'initialisation avec données d'exemple
├── 📄 start.py                     # Script de démarrage Python multiplateforme
├── 📄 start.bat                    # Script de démarrage Windows
├── 📄 app.html                     # Version HTML originale (référence)
├── 📄 gestion_industrielle.db      # Base de données SQLite (créée automatiquement)
│
├── 📁 templates/                   # Templates HTML Jinja2
│   ├── 📄 base.html               # Template de base avec navigation
│   ├── 📄 dashboard.html          # Page tableau de bord
│   ├── 📄 maintenance.html        # Module maintenance avec modales
│   ├── 📄 production.html         # Module production
│   ├── 📄 personnel.html          # Module personnel
│   └── 📄 help.html               # Page d'aide et documentation
│
├── 📁 static/                      # Fichiers statiques
│   ├── 📁 css/
│   │   └── 📄 style.css           # Styles CSS complets avec modales
│   └── 📁 js/
│       └── 📄 main.js             # JavaScript principal avec notifications
│
└── 📁 __pycache__/                 # Cache Python (généré automatiquement)
    ├── 📄 database.cpython-313.pyc
    └── 📄 models.cpython-313.pyc
```

## 📋 Description des Fichiers

### 🔧 Configuration et Démarrage
- **`app.py`** : Application Flask principale avec toutes les routes et APIs
- **`config.py`** : Configuration centralisée (développement, production, test)
- **`database.py`** : Factory pour créer l'application Flask et configurer la DB
- **`models.py`** : Modèles SQLAlchemy pour toutes les entités métier
- **`start.py`** / **`start.bat`** : Scripts de démarrage automatique

### 🗄️ Base de Données
- **`init_db.py`** : Initialisation avec 30 jours de données d'exemple
- **`gestion_industrielle.db`** : Base SQLite avec toutes les tables

### 🎨 Interface Utilisateur
- **`templates/`** : Pages HTML avec système de templates Jinja2
- **`static/css/style.css`** : Design moderne avec thème sombre
- **`static/js/main.js`** : Interactions, modales, notifications

## 🔄 Flux de Données

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend       │    │   Database      │
│   (HTML/JS)     │◄──►│   (Flask)       │◄──►│   (SQLite)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
    ┌────▼────┐             ┌────▼────┐             ┌────▼────┐
    │ Charts  │             │ API     │             │ Models  │
    │ Modales │             │ Routes  │             │ Tables  │
    │ Tables  │             │ CRUD    │             │ Data    │
    └─────────┘             └─────────┘             └─────────┘
```

## 🚀 APIs Disponibles

### Dashboard
- `GET /api/dashboard/kpis` - KPIs principaux
- `GET /api/dashboard/performance` - Performance des modules
- `GET /api/dashboard/alerts` - Alertes en temps réel
- `GET /api/dashboard/production-trend` - Tendance production 7j
- `GET /api/dashboard/statistics` - Statistiques avancées

### Maintenance (CRUD complet)
- `GET /api/maintenance/equipments` - Liste équipements
- `POST /api/maintenance/equipments` - Créer équipement
- `PUT /api/maintenance/equipments/<id>` - Modifier équipement
- `DELETE /api/maintenance/equipments/<id>` - Supprimer équipement
- `GET /api/maintenance/interventions` - Liste interventions
- `POST /api/maintenance/interventions` - Créer intervention
- `GET /api/maintenance/spare-parts` - Pièces de rechange

### Production
- `GET /api/production/daily` - Production journalière
- `GET /api/production/stocks` - Niveaux de stock

### Personnel
- `GET /api/personnel/employees` - Liste employés
- `POST /api/personnel/employees` - Créer employé
- `GET /api/personnel/attendance` - Données pointage
- `GET /api/personnel/leaves` - Gestion congés

## 🎯 Fonctionnalités Clés

### ✅ Implémentées
- ✅ Dashboard avec KPIs temps réel
- ✅ Graphiques interactifs (Chart.js)
- ✅ CRUD complet pour équipements
- ✅ Modales pour formulaires
- ✅ Système de notifications
- ✅ Navigation responsive
- ✅ Page d'aide intégrée
- ✅ Base de données avec données d'exemple
- ✅ Scripts de démarrage automatique
- ✅ API REST complète

### 🔄 Améliorations Possibles
- 🔄 Authentification utilisateur
- 🔄 Export PDF/Excel
- 🔄 Notifications push
- 🔄 API mobile
- 🔄 Intégration IoT
- 🔄 Rapports avancés
- 🔄 Backup automatique
- 🔄 Multi-langue

## 📊 Données d'Exemple

La base de données contient :
- **5 équipements** avec statuts variés
- **4 interventions** (préventive, corrective, urgente)
- **6 pièces de rechange** avec niveaux de stock
- **30 jours de production** sur 3 lignes × 3 équipes
- **5 stocks** (matières premières, produits finis)
- **8 employés** dans différents départements
- **15 jours de pointage** avec présences/absences
- **4 demandes de congés** avec statuts variés

## 🔧 Technologies

- **Backend** : Flask 3.1.1, SQLAlchemy 2.0.41
- **Frontend** : HTML5, CSS3, JavaScript ES6+
- **Base de données** : SQLite
- **Graphiques** : Chart.js
- **Icons** : Font Awesome 6.4.0
- **Responsive** : CSS Grid & Flexbox

---

**Version** : 1.0.0 Complète  
**Dernière mise à jour** : Juillet 2025
