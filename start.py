#!/usr/bin/env python3
"""
Script de démarrage pour l'Application de Gestion Industrielle
"""

import os
import sys
import subprocess
import webbrowser
import time
from pathlib import Path

def check_python_version():
    """Vérifier la version de Python"""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 ou supérieur est requis")
        print(f"Version actuelle: {sys.version}")
        return False
    print(f"✅ Python {sys.version.split()[0]} détecté")
    return True

def check_dependencies():
    """Vérifier et installer les dépendances"""
    print("🔍 Vérification des dépendances...")
    
    try:
        import flask
        import sqlalchemy
        print("✅ Dépendances déjà installées")
        return True
    except ImportError:
        print("📦 Installation des dépendances...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
            print("✅ Dépendances installées avec succès")
            return True
        except subprocess.CalledProcessError:
            print("❌ Erreur lors de l'installation des dépendances")
            return False

def check_database():
    """Vérifier et initialiser la base de données"""
    db_file = Path("gestion_industrielle.db")
    
    if not db_file.exists():
        print("🗄️ Initialisation de la base de données...")
        try:
            subprocess.check_call([sys.executable, "init_db.py"])
            print("✅ Base de données initialisée avec succès")
            return True
        except subprocess.CalledProcessError:
            print("❌ Erreur lors de l'initialisation de la base de données")
            return False
    else:
        print("✅ Base de données trouvée")
        return True

def start_application():
    """Démarrer l'application Flask"""
    print("🚀 Démarrage de l'application...")
    print("📱 L'application sera accessible à l'adresse: http://127.0.0.1:5000")
    print("🔄 Mode debug activé - l'application se rechargera automatiquement")
    print("⏹️ Appuyez sur Ctrl+C pour arrêter l'application")
    print("-" * 60)
    
    # Attendre un peu puis ouvrir le navigateur
    def open_browser():
        time.sleep(2)
        webbrowser.open("http://127.0.0.1:5000")
    
    import threading
    browser_thread = threading.Thread(target=open_browser)
    browser_thread.daemon = True
    browser_thread.start()
    
    # Démarrer Flask
    try:
        subprocess.call([sys.executable, "app.py"])
    except KeyboardInterrupt:
        print("\n👋 Application arrêtée")

def main():
    """Fonction principale"""
    print("🏭 Application de Gestion Industrielle")
    print("=" * 50)
    
    # Vérifications préliminaires
    if not check_python_version():
        return 1
    
    if not check_dependencies():
        return 1
    
    if not check_database():
        return 1
    
    # Démarrer l'application
    start_application()
    return 0

if __name__ == "__main__":
    sys.exit(main())
