// Fonctions utilitaires pour l'application
class IndustrialApp {
    constructor() {
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.updateTimestamp();
        setInterval(() => this.updateTimestamp(), 60000); // Mise à jour chaque minute
    }

    setupEventListeners() {
        // Gestion des tooltips
        this.initTooltips();
        
        // Gestion des modales
        this.initModals();
        
        // Gestion du refresh automatique
        this.setupAutoRefresh();
    }

    initTooltips() {
        // Ajouter des tooltips aux éléments avec data-tooltip
        document.querySelectorAll('[data-tooltip]').forEach(element => {
            element.addEventListener('mouseenter', (e) => {
                this.showTooltip(e.target, e.target.dataset.tooltip);
            });
            
            element.addEventListener('mouseleave', () => {
                this.hideTooltip();
            });
        });
    }

    showTooltip(element, text) {
        const tooltip = document.createElement('div');
        tooltip.className = 'tooltip';
        tooltip.textContent = text;
        document.body.appendChild(tooltip);
        
        const rect = element.getBoundingClientRect();
        tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
        tooltip.style.top = rect.top - tooltip.offsetHeight - 5 + 'px';
    }

    hideTooltip() {
        const tooltip = document.querySelector('.tooltip');
        if (tooltip) {
            tooltip.remove();
        }
    }

    initModals() {
        // Fermer les modales en cliquant sur l'overlay
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal-overlay')) {
                this.closeModal(e.target.querySelector('.modal'));
            }
        });
        
        // Fermer les modales avec Escape
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                const openModal = document.querySelector('.modal.active');
                if (openModal) {
                    this.closeModal(openModal);
                }
            }
        });
    }

    openModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.add('active');
            document.body.style.overflow = 'hidden';
        }
    }

    closeModal(modal) {
        if (modal) {
            modal.classList.remove('active');
            document.body.style.overflow = '';
        }
    }

    setupAutoRefresh() {
        // Refresh automatique des données toutes les 5 minutes
        setInterval(() => {
            if (document.visibilityState === 'visible') {
                this.refreshCurrentPageData();
            }
        }, 300000); // 5 minutes
    }

    refreshCurrentPageData() {
        const currentPage = window.location.pathname;
        
        switch (currentPage) {
            case '/':
                this.refreshDashboard();
                break;
            case '/maintenance':
                this.refreshMaintenance();
                break;
            case '/production':
                this.refreshProduction();
                break;
            case '/personnel':
                this.refreshPersonnel();
                break;
        }
    }

    refreshDashboard() {
        // Recharger les KPIs
        fetch('/api/dashboard/kpis')
            .then(response => response.json())
            .then(data => {
                document.getElementById('breakdown-rate').textContent = data.breakdown_rate + '%';
                document.getElementById('daily-production').textContent = data.daily_production.toLocaleString();
                document.getElementById('absenteeism-rate').textContent = data.absenteeism_rate + '%';
                document.getElementById('avg-stock-level').textContent = data.avg_stock_level + '%';
            })
            .catch(error => console.error('Erreur refresh KPIs:', error));
        
        // Recharger les alertes
        fetch('/api/dashboard/alerts')
            .then(response => response.json())
            .then(data => {
                const container = document.getElementById('alerts-container');
                if (data.length === 0) {
                    container.innerHTML = '<div class="no-alerts">Aucune alerte récente</div>';
                } else {
                    container.innerHTML = data.map(alert => `
                        <div class="alert-item">
                            <i class="${alert.icon}" style="color: ${alert.color};"></i>
                            ${alert.message}
                        </div>
                    `).join('');
                }
            })
            .catch(error => console.error('Erreur refresh alertes:', error));
    }

    refreshMaintenance() {
        // Recharger les données de maintenance
        if (typeof loadMaintenanceData === 'function') {
            loadMaintenanceData();
        }
    }

    refreshProduction() {
        // Recharger les données de production
        if (typeof loadProductionData === 'function') {
            loadProductionData();
        }
    }

    refreshPersonnel() {
        // Recharger les données de personnel
        if (typeof loadPersonnelData === 'function') {
            loadPersonnelData();
        }
    }

    updateTimestamp() {
        const now = new Date();
        const timestamp = now.toLocaleString('fr-FR', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
        
        const timestampElements = document.querySelectorAll('.timestamp');
        timestampElements.forEach(element => {
            element.textContent = `Dernière mise à jour: ${timestamp}`;
        });
    }

    // Utilitaires pour les tableaux
    createDataTable(containerId, data, columns) {
        const container = document.getElementById(containerId);
        if (!container) return;

        const table = document.createElement('table');
        table.className = 'data-table';

        // En-têtes
        const thead = document.createElement('thead');
        const headerRow = document.createElement('tr');
        columns.forEach(col => {
            const th = document.createElement('th');
            th.textContent = col.title;
            headerRow.appendChild(th);
        });
        thead.appendChild(headerRow);
        table.appendChild(thead);

        // Corps du tableau
        const tbody = document.createElement('tbody');
        data.forEach(row => {
            const tr = document.createElement('tr');
            columns.forEach(col => {
                const td = document.createElement('td');
                if (col.render) {
                    td.innerHTML = col.render(row[col.field], row);
                } else {
                    td.textContent = row[col.field] || '-';
                }
                tr.appendChild(td);
            });
            tbody.appendChild(tr);
        });
        table.appendChild(tbody);

        container.innerHTML = '';
        container.appendChild(table);
    }

    // Utilitaires pour les status badges
    getStatusBadge(status, type = 'default') {
        const statusClasses = {
            'Opérationnel': 'status-success',
            'En panne': 'status-critical',
            'Maintenance': 'status-warning',
            'Normal': 'status-success',
            'Critique': 'status-critical',
            'Bas': 'status-warning',
            'Présent': 'status-success',
            'Absent': 'status-critical',
            'Retard': 'status-warning',
            'Terminé': 'status-success',
            'En cours': 'status-warning',
            'Approuvé': 'status-success',
            'En attente': 'status-warning',
            'Refusé': 'status-critical'
        };

        const className = statusClasses[status] || 'status-normal';
        return `<span class="status-badge ${className}">${status}</span>`;
    }

    // Formatage des nombres
    formatNumber(number, decimals = 0) {
        return new Intl.NumberFormat('fr-FR', {
            minimumFractionDigits: decimals,
            maximumFractionDigits: decimals
        }).format(number);
    }

    // Formatage des dates
    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('fr-FR');
    }

    formatDateTime(dateString) {
        const date = new Date(dateString);
        return date.toLocaleString('fr-FR');
    }

    // Notification système améliorée
    showNotification(message, type = 'info', duration = 4000) {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;

        // Icônes selon le type
        const icons = {
            'success': 'fas fa-check-circle',
            'error': 'fas fa-exclamation-circle',
            'warning': 'fas fa-exclamation-triangle',
            'info': 'fas fa-info-circle'
        };

        notification.innerHTML = `
            <i class="${icons[type] || icons.info}"></i>
            <span>${message}</span>
            <button class="notification-close" onclick="this.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        `;

        // Conteneur de notifications
        let container = document.getElementById('notifications-container');
        if (!container) {
            container = document.createElement('div');
            container.id = 'notifications-container';
            container.className = 'notifications-container';
            document.body.appendChild(container);
        }

        container.appendChild(notification);

        // Animation d'entrée
        setTimeout(() => notification.classList.add('show'), 100);

        // Suppression automatique
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 300);
        }, duration);
    }
}

// Initialiser l'application
document.addEventListener('DOMContentLoaded', () => {
    window.industrialApp = new IndustrialApp();
});

// Styles pour les notifications et tooltips
const additionalStyles = `
.tooltip {
    position: absolute;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 0.8rem;
    z-index: 1000;
    pointer-events: none;
}

.notifications-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.notification {
    padding: 15px 20px;
    border-radius: 8px;
    color: white;
    font-weight: 500;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    display: flex;
    align-items: center;
    gap: 10px;
    min-width: 300px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.notification.show {
    transform: translateX(0);
}

.notification i:first-child {
    font-size: 1.2rem;
}

.notification span {
    flex: 1;
}

.notification-close {
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.8);
    cursor: pointer;
    padding: 2px;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.notification-close:hover {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

.notification-info { background: linear-gradient(135deg, #3498db, #2980b9); }
.notification-success { background: linear-gradient(135deg, #27ae60, #229954); }
.notification-warning { background: linear-gradient(135deg, #f39c12, #e67e22); }
.notification-error { background: linear-gradient(135deg, #e74c3c, #c0392b); }

.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-overlay.active {
    display: flex;
}

.modal {
    background: var(--primary);
    border-radius: 10px;
    padding: 20px;
    max-width: 90%;
    max-height: 90%;
    overflow-y: auto;
}
`;

// Ajouter les styles supplémentaires
const styleSheet = document.createElement('style');
styleSheet.textContent = additionalStyles;
document.head.appendChild(styleSheet);
