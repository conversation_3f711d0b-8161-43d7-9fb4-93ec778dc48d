{% extends "base.html" %}

{% block title %}Gestion Maintenance - {{ super() }}{% endblock %}

{% block content %}
<div class="header">
    <h2><i class="fas fa-tools"></i> Gestion de Maintenance</h2>
    <div class="user-info">
        <img src="https://ui-avatars.com/api/?name=Admin&background=3498db&color=fff" alt="User">
        <span>Responsable Maintenance</span>
    </div>
</div>

<!-- Onglets de navigation -->
<div class="tabs-container">
    <div class="tabs">
        <button class="tab-button active" data-tab="equipments">
            <i class="fas fa-list"></i> Équipements
        </button>
        <button class="tab-button" data-tab="interventions">
            <i class="fas fa-wrench"></i> Interventions
        </button>
        <button class="tab-button" data-tab="spare-parts">
            <i class="fas fa-cogs"></i> Pièces de Rechange
        </button>
    </div>
</div>

<!-- Contenu des onglets -->
<div class="tab-content">
    <!-- Onglet Équipements -->
    <div id="equipments" class="tab-pane active">
        <div class="section-header">
            <h3><i class="fas fa-list"></i> Liste des Équipements</h3>
            <div class="section-actions">
                <button class="btn btn-primary" onclick="openEquipmentModal()">
                    <i class="fas fa-plus"></i> Nouvel Équipement
                </button>
                <div class="timestamp"></div>
            </div>
        </div>
        <div class="data-table">
            <div id="equipments-table" class="loading">Chargement des équipements...</div>
        </div>
    </div>

    <!-- Onglet Interventions -->
    <div id="interventions" class="tab-pane">
        <div class="section-header">
            <h3><i class="fas fa-wrench"></i> Interventions Récentes</h3>
            <div class="section-actions">
                <button class="btn btn-primary" onclick="openInterventionModal()">
                    <i class="fas fa-plus"></i> Nouvelle Intervention
                </button>
                <div class="timestamp"></div>
            </div>
        </div>
        <div class="data-table">
            <div id="interventions-table" class="loading">Chargement des interventions...</div>
        </div>
    </div>

    <!-- Onglet Pièces de Rechange -->
    <div id="spare-parts" class="tab-pane">
        <div class="section-header">
            <h3><i class="fas fa-cogs"></i> Stock des Pièces de Rechange</h3>
            <div class="timestamp"></div>
        </div>
        <div class="data-table">
            <div id="spare-parts-table" class="loading">Chargement des pièces...</div>
        </div>
    </div>
</div>

<!-- Modales -->
<!-- Modal Équipement -->
<div id="equipmentModal" class="modal-overlay">
    <div class="modal">
        <div class="modal-header">
            <h3><i class="fas fa-cogs"></i> <span id="equipmentModalTitle">Nouvel Équipement</span></h3>
            <button class="modal-close" onclick="closeEquipmentModal()">&times;</button>
        </div>
        <form id="equipmentForm">
            <div class="form-group">
                <label for="equipmentName">Nom de l'équipement *</label>
                <input type="text" id="equipmentName" name="name" required>
            </div>
            <div class="form-group">
                <label for="equipmentCode">Code *</label>
                <input type="text" id="equipmentCode" name="code" required>
            </div>
            <div class="form-group">
                <label for="equipmentType">Type *</label>
                <select id="equipmentType" name="type" required>
                    <option value="">Sélectionner un type</option>
                    <option value="Presse">Presse</option>
                    <option value="Tour">Tour</option>
                    <option value="Fraiseuse">Fraiseuse</option>
                    <option value="Convoyeur">Convoyeur</option>
                    <option value="Robot">Robot</option>
                    <option value="Autre">Autre</option>
                </select>
            </div>
            <div class="form-group">
                <label for="equipmentLocation">Localisation *</label>
                <input type="text" id="equipmentLocation" name="location" required>
            </div>
            <div class="form-group">
                <label for="equipmentInstallDate">Date d'installation *</label>
                <input type="date" id="equipmentInstallDate" name="installation_date" required>
            </div>
            <div class="form-group">
                <label for="equipmentStatus">Statut</label>
                <select id="equipmentStatus" name="status">
                    <option value="Opérationnel">Opérationnel</option>
                    <option value="En panne">En panne</option>
                    <option value="Maintenance">Maintenance</option>
                </select>
            </div>
            <div class="modal-actions">
                <button type="button" class="btn btn-secondary" onclick="closeEquipmentModal()">Annuler</button>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> Enregistrer
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Modal Intervention -->
<div id="interventionModal" class="modal-overlay">
    <div class="modal">
        <div class="modal-header">
            <h3><i class="fas fa-wrench"></i> Nouvelle Intervention</h3>
            <button class="modal-close" onclick="closeInterventionModal()">&times;</button>
        </div>
        <form id="interventionForm">
            <div class="form-group">
                <label for="interventionEquipment">Équipement *</label>
                <select id="interventionEquipment" name="equipment_id" required>
                    <option value="">Sélectionner un équipement</option>
                </select>
            </div>
            <div class="form-group">
                <label for="interventionType">Type d'intervention *</label>
                <select id="interventionType" name="type" required>
                    <option value="">Sélectionner un type</option>
                    <option value="Préventive">Préventive</option>
                    <option value="Corrective">Corrective</option>
                    <option value="Urgente">Urgente</option>
                </select>
            </div>
            <div class="form-group">
                <label for="interventionDescription">Description *</label>
                <textarea id="interventionDescription" name="description" rows="3" required></textarea>
            </div>
            <div class="form-group">
                <label for="interventionTechnician">Technicien *</label>
                <input type="text" id="interventionTechnician" name="technician" required>
            </div>
            <div class="form-group">
                <label for="interventionStartTime">Date et heure de début *</label>
                <input type="datetime-local" id="interventionStartTime" name="start_time" required>
            </div>
            <div class="form-group">
                <label for="interventionEndTime">Date et heure de fin</label>
                <input type="datetime-local" id="interventionEndTime" name="end_time">
            </div>
            <div class="form-group">
                <label for="interventionCost">Coût estimé (€)</label>
                <input type="number" id="interventionCost" name="cost" step="0.01" min="0">
            </div>
            <div class="modal-actions">
                <button type="button" class="btn btn-secondary" onclick="closeInterventionModal()">Annuler</button>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> Enregistrer
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Gestion des onglets
    const tabButtons = document.querySelectorAll('.tab-button');
    const tabPanes = document.querySelectorAll('.tab-pane');

    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            const tabId = button.dataset.tab;
            
            // Désactiver tous les onglets
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabPanes.forEach(pane => pane.classList.remove('active'));
            
            // Activer l'onglet sélectionné
            button.classList.add('active');
            document.getElementById(tabId).classList.add('active');
            
            // Charger les données si nécessaire
            loadTabData(tabId);
        });
    });

    // Charger les données initiales
    loadMaintenanceData();
});

function loadMaintenanceData() {
    loadEquipments();
    loadInterventions();
    loadSpareParts();
}

function loadTabData(tabId) {
    switch(tabId) {
        case 'equipments':
            loadEquipments();
            break;
        case 'interventions':
            loadInterventions();
            break;
        case 'spare-parts':
            loadSpareParts();
            break;
    }
}

function loadEquipments() {
    fetch('/api/maintenance/equipments')
        .then(response => response.json())
        .then(data => {
            const columns = [
                { field: 'code', title: 'Code' },
                { field: 'name', title: 'Nom' },
                { field: 'type', title: 'Type' },
                { field: 'location', title: 'Localisation' },
                {
                    field: 'status',
                    title: 'Statut',
                    render: (value) => window.industrialApp.getStatusBadge(value)
                },
                {
                    field: 'last_maintenance',
                    title: 'Dernière Maintenance',
                    render: (value) => value ? window.industrialApp.formatDate(value) : '-'
                },
                {
                    field: 'next_maintenance',
                    title: 'Prochaine Maintenance',
                    render: (value) => value ? window.industrialApp.formatDate(value) : '-'
                },
                {
                    field: 'id',
                    title: 'Actions',
                    render: (value, row) => `
                        <button class="btn btn-sm btn-secondary" onclick="editEquipment(${value})" title="Modifier">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="deleteEquipment(${value})" title="Supprimer">
                            <i class="fas fa-trash"></i>
                        </button>
                    `
                }
            ];
            
            window.industrialApp.createDataTable('equipments-table', data, columns);
        })
        .catch(error => {
            console.error('Erreur lors du chargement des équipements:', error);
            document.getElementById('equipments-table').innerHTML = '<div class="error">Erreur de chargement</div>';
        });
}

function loadInterventions() {
    fetch('/api/maintenance/interventions')
        .then(response => response.json())
        .then(data => {
            const columns = [
                { field: 'equipment_name', title: 'Équipement' },
                { 
                    field: 'type', 
                    title: 'Type',
                    render: (value) => {
                        const colors = {
                            'Préventive': '#3498db',
                            'Corrective': '#f39c12',
                            'Urgente': '#e74c3c'
                        };
                        return `<span style="color: ${colors[value] || '#fff'}">${value}</span>`;
                    }
                },
                { field: 'description', title: 'Description' },
                { field: 'technician', title: 'Technicien' },
                { 
                    field: 'start_time', 
                    title: 'Début',
                    render: (value) => value ? window.industrialApp.formatDateTime(value) : '-'
                },
                { 
                    field: 'cost', 
                    title: 'Coût (€)',
                    render: (value) => window.industrialApp.formatNumber(value, 2)
                },
                { 
                    field: 'status', 
                    title: 'Statut',
                    render: (value) => window.industrialApp.getStatusBadge(value)
                }
            ];
            
            window.industrialApp.createDataTable('interventions-table', data, columns);
        })
        .catch(error => {
            console.error('Erreur lors du chargement des interventions:', error);
            document.getElementById('interventions-table').innerHTML = '<div class="error">Erreur de chargement</div>';
        });
}

function loadSpareParts() {
    fetch('/api/maintenance/spare-parts')
        .then(response => response.json())
        .then(data => {
            const columns = [
                { field: 'reference', title: 'Référence' },
                { field: 'name', title: 'Nom' },
                { field: 'equipment_name', title: 'Équipement' },
                { 
                    field: 'quantity_stock', 
                    title: 'Stock',
                    render: (value, row) => {
                        const color = value <= row.quantity_min ? '#e74c3c' : '#2ecc71';
                        return `<span style="color: ${color}">${value}</span>`;
                    }
                },
                { field: 'quantity_min', title: 'Stock Min' },
                { 
                    field: 'unit_price', 
                    title: 'Prix Unitaire (€)',
                    render: (value) => window.industrialApp.formatNumber(value, 2)
                },
                { field: 'supplier', title: 'Fournisseur' },
                { 
                    field: 'status', 
                    title: 'Statut',
                    render: (value) => window.industrialApp.getStatusBadge(value)
                }
            ];
            
            window.industrialApp.createDataTable('spare-parts-table', data, columns);
        })
        .catch(error => {
            console.error('Erreur lors du chargement des pièces:', error);
            document.getElementById('spare-parts-table').innerHTML = '<div class="error">Erreur de chargement</div>';
        });
}

// Fonctions pour les modales
function openEquipmentModal(equipmentId = null) {
    const modal = document.getElementById('equipmentModal');
    const form = document.getElementById('equipmentForm');
    const title = document.getElementById('equipmentModalTitle');

    if (equipmentId) {
        title.textContent = 'Modifier l\'Équipement';
        // Charger les données de l'équipement pour modification
        loadEquipmentData(equipmentId);
    } else {
        title.textContent = 'Nouvel Équipement';
        form.reset();
    }

    modal.classList.add('active');
}

function closeEquipmentModal() {
    document.getElementById('equipmentModal').classList.remove('active');
}

function openInterventionModal() {
    const modal = document.getElementById('interventionModal');
    const form = document.getElementById('interventionForm');

    form.reset();
    loadEquipmentOptions();
    modal.classList.add('active');
}

function closeInterventionModal() {
    document.getElementById('interventionModal').classList.remove('active');
}

function loadEquipmentOptions() {
    fetch('/api/maintenance/equipments')
        .then(response => response.json())
        .then(data => {
            const select = document.getElementById('interventionEquipment');
            select.innerHTML = '<option value="">Sélectionner un équipement</option>';
            data.forEach(equipment => {
                select.innerHTML += `<option value="${equipment.id}">${equipment.name} (${equipment.code})</option>`;
            });
        })
        .catch(error => console.error('Erreur lors du chargement des équipements:', error));
}

function editEquipment(equipmentId) {
    openEquipmentModal(equipmentId);
}

function deleteEquipment(equipmentId) {
    if (confirm('Êtes-vous sûr de vouloir supprimer cet équipement ?')) {
        fetch(`/api/maintenance/equipments/${equipmentId}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                alert('Erreur: ' + data.error);
            } else {
                window.industrialApp.showNotification('Équipement supprimé avec succès', 'success');
                loadEquipments();
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            alert('Erreur lors de la suppression');
        });
    }
}

// Gestion des formulaires
document.getElementById('equipmentForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    const data = Object.fromEntries(formData.entries());

    fetch('/api/maintenance/equipments', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.error) {
            alert('Erreur: ' + result.error);
        } else {
            window.industrialApp.showNotification('Équipement créé avec succès', 'success');
            closeEquipmentModal();
            loadEquipments();
        }
    })
    .catch(error => {
        console.error('Erreur:', error);
        alert('Erreur lors de la création');
    });
});

document.getElementById('interventionForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    const data = Object.fromEntries(formData.entries());

    fetch('/api/maintenance/interventions', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.error) {
            alert('Erreur: ' + result.error);
        } else {
            window.industrialApp.showNotification('Intervention créée avec succès', 'success');
            closeInterventionModal();
            loadInterventions();
        }
    })
    .catch(error => {
        console.error('Erreur:', error);
        alert('Erreur lors de la création');
    });
});
</script>
{% endblock %}
