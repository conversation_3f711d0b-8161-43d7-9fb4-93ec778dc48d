{% extends "base.html" %}

{% block title %}Gestion Maintenance - {{ super() }}{% endblock %}

{% block content %}
<div class="header">
    <h2><i class="fas fa-tools"></i> Gestion de Maintenance</h2>
    <div class="user-info">
        <img src="https://ui-avatars.com/api/?name=Admin&background=3498db&color=fff" alt="User">
        <span>Responsable Maintenance</span>
    </div>
</div>

<!-- Onglets de navigation -->
<div class="tabs-container">
    <div class="tabs">
        <button class="tab-button active" data-tab="equipments">
            <i class="fas fa-list"></i> Équipements
        </button>
        <button class="tab-button" data-tab="interventions">
            <i class="fas fa-wrench"></i> Interventions
        </button>
        <button class="tab-button" data-tab="spare-parts">
            <i class="fas fa-cogs"></i> Pièces de Rechange
        </button>
    </div>
</div>

<!-- Contenu des onglets -->
<div class="tab-content">
    <!-- Onglet Équipements -->
    <div id="equipments" class="tab-pane active">
        <div class="section-header">
            <h3><i class="fas fa-list"></i> Liste des Équipements</h3>
            <div class="timestamp"></div>
        </div>
        <div class="data-table">
            <div id="equipments-table" class="loading">Chargement des équipements...</div>
        </div>
    </div>

    <!-- Onglet Interventions -->
    <div id="interventions" class="tab-pane">
        <div class="section-header">
            <h3><i class="fas fa-wrench"></i> Interventions Récentes</h3>
            <div class="timestamp"></div>
        </div>
        <div class="data-table">
            <div id="interventions-table" class="loading">Chargement des interventions...</div>
        </div>
    </div>

    <!-- Onglet Pièces de Rechange -->
    <div id="spare-parts" class="tab-pane">
        <div class="section-header">
            <h3><i class="fas fa-cogs"></i> Stock des Pièces de Rechange</h3>
            <div class="timestamp"></div>
        </div>
        <div class="data-table">
            <div id="spare-parts-table" class="loading">Chargement des pièces...</div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Gestion des onglets
    const tabButtons = document.querySelectorAll('.tab-button');
    const tabPanes = document.querySelectorAll('.tab-pane');

    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            const tabId = button.dataset.tab;
            
            // Désactiver tous les onglets
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabPanes.forEach(pane => pane.classList.remove('active'));
            
            // Activer l'onglet sélectionné
            button.classList.add('active');
            document.getElementById(tabId).classList.add('active');
            
            // Charger les données si nécessaire
            loadTabData(tabId);
        });
    });

    // Charger les données initiales
    loadMaintenanceData();
});

function loadMaintenanceData() {
    loadEquipments();
    loadInterventions();
    loadSpareParts();
}

function loadTabData(tabId) {
    switch(tabId) {
        case 'equipments':
            loadEquipments();
            break;
        case 'interventions':
            loadInterventions();
            break;
        case 'spare-parts':
            loadSpareParts();
            break;
    }
}

function loadEquipments() {
    fetch('/api/maintenance/equipments')
        .then(response => response.json())
        .then(data => {
            const columns = [
                { field: 'code', title: 'Code' },
                { field: 'name', title: 'Nom' },
                { field: 'type', title: 'Type' },
                { field: 'location', title: 'Localisation' },
                { 
                    field: 'status', 
                    title: 'Statut',
                    render: (value) => window.industrialApp.getStatusBadge(value)
                },
                { 
                    field: 'last_maintenance', 
                    title: 'Dernière Maintenance',
                    render: (value) => value ? window.industrialApp.formatDate(value) : '-'
                },
                { 
                    field: 'next_maintenance', 
                    title: 'Prochaine Maintenance',
                    render: (value) => value ? window.industrialApp.formatDate(value) : '-'
                }
            ];
            
            window.industrialApp.createDataTable('equipments-table', data, columns);
        })
        .catch(error => {
            console.error('Erreur lors du chargement des équipements:', error);
            document.getElementById('equipments-table').innerHTML = '<div class="error">Erreur de chargement</div>';
        });
}

function loadInterventions() {
    fetch('/api/maintenance/interventions')
        .then(response => response.json())
        .then(data => {
            const columns = [
                { field: 'equipment_name', title: 'Équipement' },
                { 
                    field: 'type', 
                    title: 'Type',
                    render: (value) => {
                        const colors = {
                            'Préventive': '#3498db',
                            'Corrective': '#f39c12',
                            'Urgente': '#e74c3c'
                        };
                        return `<span style="color: ${colors[value] || '#fff'}">${value}</span>`;
                    }
                },
                { field: 'description', title: 'Description' },
                { field: 'technician', title: 'Technicien' },
                { 
                    field: 'start_time', 
                    title: 'Début',
                    render: (value) => value ? window.industrialApp.formatDateTime(value) : '-'
                },
                { 
                    field: 'cost', 
                    title: 'Coût (€)',
                    render: (value) => window.industrialApp.formatNumber(value, 2)
                },
                { 
                    field: 'status', 
                    title: 'Statut',
                    render: (value) => window.industrialApp.getStatusBadge(value)
                }
            ];
            
            window.industrialApp.createDataTable('interventions-table', data, columns);
        })
        .catch(error => {
            console.error('Erreur lors du chargement des interventions:', error);
            document.getElementById('interventions-table').innerHTML = '<div class="error">Erreur de chargement</div>';
        });
}

function loadSpareParts() {
    fetch('/api/maintenance/spare-parts')
        .then(response => response.json())
        .then(data => {
            const columns = [
                { field: 'reference', title: 'Référence' },
                { field: 'name', title: 'Nom' },
                { field: 'equipment_name', title: 'Équipement' },
                { 
                    field: 'quantity_stock', 
                    title: 'Stock',
                    render: (value, row) => {
                        const color = value <= row.quantity_min ? '#e74c3c' : '#2ecc71';
                        return `<span style="color: ${color}">${value}</span>`;
                    }
                },
                { field: 'quantity_min', title: 'Stock Min' },
                { 
                    field: 'unit_price', 
                    title: 'Prix Unitaire (€)',
                    render: (value) => window.industrialApp.formatNumber(value, 2)
                },
                { field: 'supplier', title: 'Fournisseur' },
                { 
                    field: 'status', 
                    title: 'Statut',
                    render: (value) => window.industrialApp.getStatusBadge(value)
                }
            ];
            
            window.industrialApp.createDataTable('spare-parts-table', data, columns);
        })
        .catch(error => {
            console.error('Erreur lors du chargement des pièces:', error);
            document.getElementById('spare-parts-table').innerHTML = '<div class="error">Erreur de chargement</div>';
        });
}
</script>
{% endblock %}
