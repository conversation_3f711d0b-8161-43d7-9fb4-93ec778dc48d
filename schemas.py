"""
Schémas Pydantic pour la validation des données
"""

from pydantic import BaseModel, Field, ConfigDict
from typing import Optional, List
from datetime import date, datetime, time
from enum import Enum

# Enums pour les choix
class EquipmentStatus(str, Enum):
    OPERATIONAL = "Opérationnel"
    BROKEN = "En panne"
    MAINTENANCE = "Maintenance"

class InterventionType(str, Enum):
    PREVENTIVE = "Préventive"
    CORRECTIVE = "Corrective"
    URGENT = "Urgente"

class InterventionStatus(str, Enum):
    IN_PROGRESS = "En cours"
    COMPLETED = "Terminé"
    CANCELLED = "Annulé"

class StockCategory(str, Enum):
    RAW_MATERIAL = "Matière première"
    FINISHED_PRODUCT = "Produit fini"
    SEMI_FINISHED = "Semi-fini"

class EmployeeStatus(str, Enum):
    ACTIVE = "Actif"
    INACTIVE = "Inactif"

class AttendanceStatus(str, Enum):
    PRESENT = "Présent"
    ABSENT = "Absent"
    LATE = "Retard"

class LeaveStatus(str, Enum):
    PENDING = "En attente"
    APPROVED = "Approuvé"
    REJECTED = "Refusé"

# Schémas de base
class EquipmentBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=100)
    code: str = Field(..., min_length=1, max_length=50)
    type: str = Field(..., min_length=1, max_length=50)
    location: str = Field(..., min_length=1, max_length=100)
    status: EquipmentStatus = EquipmentStatus.OPERATIONAL
    installation_date: date
    last_maintenance: Optional[date] = None
    next_maintenance: Optional[date] = None

class EquipmentCreate(EquipmentBase):
    pass

class EquipmentUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    code: Optional[str] = Field(None, min_length=1, max_length=50)
    type: Optional[str] = Field(None, min_length=1, max_length=50)
    location: Optional[str] = Field(None, min_length=1, max_length=100)
    status: Optional[EquipmentStatus] = None
    installation_date: Optional[date] = None
    last_maintenance: Optional[date] = None
    next_maintenance: Optional[date] = None

class Equipment(EquipmentBase):
    model_config = ConfigDict(from_attributes=True)
    
    id: int
    created_at: datetime

class InterventionBase(BaseModel):
    equipment_id: int
    type: InterventionType
    description: str = Field(..., min_length=1)
    start_time: datetime
    end_time: Optional[datetime] = None
    technician: str = Field(..., min_length=1, max_length=100)
    cost: float = Field(default=0.0, ge=0)
    status: InterventionStatus = InterventionStatus.IN_PROGRESS

class InterventionCreate(InterventionBase):
    pass

class Intervention(InterventionBase):
    model_config = ConfigDict(from_attributes=True)
    
    id: int
    created_at: datetime
    equipment: Equipment

class SparePartBase(BaseModel):
    equipment_id: int
    name: str = Field(..., min_length=1, max_length=100)
    reference: str = Field(..., min_length=1, max_length=50)
    quantity_stock: int = Field(default=0, ge=0)
    quantity_min: int = Field(default=5, ge=0)
    unit_price: float = Field(..., gt=0)
    supplier: Optional[str] = Field(None, max_length=100)

class SparePartCreate(SparePartBase):
    pass

class SparePart(SparePartBase):
    model_config = ConfigDict(from_attributes=True)
    
    id: int
    created_at: datetime
    equipment: Equipment

class ProductionBase(BaseModel):
    date: date
    line: str = Field(..., min_length=1, max_length=50)
    shift: str = Field(..., min_length=1, max_length=20)
    product_type: str = Field(..., min_length=1, max_length=100)
    quantity_produced: int = Field(..., ge=0)
    quantity_target: int = Field(..., gt=0)
    quality_rate: float = Field(default=100.0, ge=0, le=100)
    downtime_minutes: int = Field(default=0, ge=0)

class ProductionCreate(ProductionBase):
    pass

class Production(ProductionBase):
    model_config = ConfigDict(from_attributes=True)
    
    id: int
    created_at: datetime

class StockBase(BaseModel):
    product_name: str = Field(..., min_length=1, max_length=100)
    product_code: str = Field(..., min_length=1, max_length=50)
    category: StockCategory
    quantity_current: int = Field(default=0, ge=0)
    quantity_min: int = Field(default=10, ge=0)
    quantity_max: int = Field(default=1000, gt=0)
    unit: str = Field(default="pcs", max_length=20)
    location: Optional[str] = Field(None, max_length=100)

class StockCreate(StockBase):
    pass

class Stock(StockBase):
    model_config = ConfigDict(from_attributes=True)
    
    id: int
    last_movement: Optional[datetime] = None
    created_at: datetime

class EmployeeBase(BaseModel):
    employee_id: str = Field(..., min_length=1, max_length=20)
    first_name: str = Field(..., min_length=1, max_length=50)
    last_name: str = Field(..., min_length=1, max_length=50)
    position: str = Field(..., min_length=1, max_length=100)
    department: str = Field(..., min_length=1, max_length=50)
    hire_date: date
    salary: Optional[float] = Field(None, gt=0)
    status: EmployeeStatus = EmployeeStatus.ACTIVE

class EmployeeCreate(EmployeeBase):
    pass

class Employee(EmployeeBase):
    model_config = ConfigDict(from_attributes=True)
    
    id: int
    created_at: datetime

class TimeRecordBase(BaseModel):
    employee_id: int
    date: date
    check_in: Optional[time] = None
    check_out: Optional[time] = None
    break_duration: int = Field(default=0, ge=0)
    overtime_hours: float = Field(default=0.0, ge=0)
    status: AttendanceStatus = AttendanceStatus.PRESENT

class TimeRecordCreate(TimeRecordBase):
    pass

class TimeRecord(TimeRecordBase):
    model_config = ConfigDict(from_attributes=True)
    
    id: int
    created_at: datetime
    employee: Employee

class LeaveBase(BaseModel):
    employee_id: int
    type: str = Field(..., min_length=1, max_length=50)
    start_date: date
    end_date: date
    days_count: int = Field(..., gt=0)
    status: LeaveStatus = LeaveStatus.PENDING
    reason: Optional[str] = None

class LeaveCreate(LeaveBase):
    pass

class Leave(LeaveBase):
    model_config = ConfigDict(from_attributes=True)
    
    id: int
    created_at: datetime
    employee: Employee

# Schémas pour les réponses API
class DashboardKPIs(BaseModel):
    breakdown_rate: float
    daily_production: int
    absenteeism_rate: float
    avg_stock_level: float

class PerformanceData(BaseModel):
    maintenance: int
    production: int
    personnel: int

class Alert(BaseModel):
    type: str
    message: str
    icon: str
    color: str

class ProductionTrendDataset(BaseModel):
    label: str
    data: List[int]
    borderColor: str
    backgroundColor: str
    tension: float

class ProductionTrend(BaseModel):
    labels: List[str]
    datasets: List[ProductionTrendDataset]

class Statistics(BaseModel):
    maintenance: dict
    production: dict
    personnel: dict
