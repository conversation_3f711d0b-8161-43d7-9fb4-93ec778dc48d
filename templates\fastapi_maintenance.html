{% extends "fastapi_base.html" %}

{% block title %}Gestion Maintenance FastAPI - {{ super() }}{% endblock %}

{% block content %}
<div class="header">
    <h2>
        <i class="fas fa-tools"></i> 
        Gestion de Maintenance
        <span class="fastapi-badge">FastAPI</span>
    </h2>
    <div class="user-info">
        <img src="https://ui-avatars.com/api/?name=Maintenance&background=3498db&color=fff" alt="User">
        <span>Responsable Maintenance</span>
    </div>
</div>

<!-- Onglets de navigation -->
<div class="tabs-container">
    <div class="tabs">
        <button class="tab-button active" data-tab="equipments">
            <i class="fas fa-list"></i> Équipements
        </button>
        <button class="tab-button" data-tab="interventions">
            <i class="fas fa-wrench"></i> Interventions
        </button>
        <button class="tab-button" data-tab="spare-parts">
            <i class="fas fa-cogs"></i> Pièces de Rechange
        </button>
    </div>
</div>

<!-- Contenu des onglets -->
<div class="tab-content">
    <!-- Onglet Équipements -->
    <div id="equipments" class="tab-pane active">
        <div class="section-header">
            <h3><i class="fas fa-list"></i> Liste des Équipements</h3>
            <div class="section-actions">
                <button class="btn btn-primary" onclick="openEquipmentModal()">
                    <i class="fas fa-plus"></i> Nouvel Équipement
                </button>
                <div class="timestamp"></div>
            </div>
        </div>
        <div class="data-table">
            <div id="equipments-table" class="loading">Chargement des équipements...</div>
        </div>
    </div>

    <!-- Onglet Interventions -->
    <div id="interventions" class="tab-pane">
        <div class="section-header">
            <h3><i class="fas fa-wrench"></i> Interventions Récentes</h3>
            <div class="section-actions">
                <button class="btn btn-primary" onclick="openInterventionModal()">
                    <i class="fas fa-plus"></i> Nouvelle Intervention
                </button>
                <div class="timestamp"></div>
            </div>
        </div>
        <div class="data-table">
            <div id="interventions-table" class="loading">Chargement des interventions...</div>
        </div>
    </div>

    <!-- Onglet Pièces de Rechange -->
    <div id="spare-parts" class="tab-pane">
        <div class="section-header">
            <h3><i class="fas fa-cogs"></i> Stock des Pièces de Rechange</h3>
            <div class="timestamp"></div>
        </div>
        <div class="data-table">
            <div id="spare-parts-table" class="loading">Chargement des pièces...</div>
        </div>
    </div>
</div>

<!-- Modales -->
<!-- Modal Équipement -->
<div id="equipmentModal" class="modal-overlay">
    <div class="modal">
        <div class="modal-header">
            <h3><i class="fas fa-cogs"></i> <span id="equipmentModalTitle">Nouvel Équipement</span></h3>
            <button class="modal-close" onclick="closeEquipmentModal()">&times;</button>
        </div>
        <form id="equipmentForm">
            <div class="form-group">
                <label for="equipmentName">Nom de l'équipement *</label>
                <input type="text" id="equipmentName" name="name" required>
            </div>
            <div class="form-group">
                <label for="equipmentCode">Code *</label>
                <input type="text" id="equipmentCode" name="code" required>
            </div>
            <div class="form-group">
                <label for="equipmentType">Type *</label>
                <select id="equipmentType" name="type" required>
                    <option value="">Sélectionner un type</option>
                    <option value="Presse">Presse</option>
                    <option value="Tour">Tour</option>
                    <option value="Fraiseuse">Fraiseuse</option>
                    <option value="Convoyeur">Convoyeur</option>
                    <option value="Robot">Robot</option>
                    <option value="Autre">Autre</option>
                </select>
            </div>
            <div class="form-group">
                <label for="equipmentLocation">Localisation *</label>
                <input type="text" id="equipmentLocation" name="location" required>
            </div>
            <div class="form-group">
                <label for="equipmentInstallDate">Date d'installation *</label>
                <input type="date" id="equipmentInstallDate" name="installation_date" required>
            </div>
            <div class="form-group">
                <label for="equipmentStatus">Statut</label>
                <select id="equipmentStatus" name="status">
                    <option value="Opérationnel">Opérationnel</option>
                    <option value="En panne">En panne</option>
                    <option value="Maintenance">Maintenance</option>
                </select>
            </div>
            <div class="modal-actions">
                <button type="button" class="btn btn-secondary" onclick="closeEquipmentModal()">Annuler</button>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> Enregistrer
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Gestion des onglets
    const tabButtons = document.querySelectorAll('.tab-button');
    const tabPanes = document.querySelectorAll('.tab-pane');

    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            const tabId = button.dataset.tab;
            
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabPanes.forEach(pane => pane.classList.remove('active'));
            
            button.classList.add('active');
            document.getElementById(tabId).classList.add('active');
            
            loadTabData(tabId);
        });
    });

    loadMaintenanceData();
});

function loadMaintenanceData() {
    loadEquipments();
    loadInterventions();
    loadSpareParts();
}

function loadTabData(tabId) {
    switch(tabId) {
        case 'equipments':
            loadEquipments();
            break;
        case 'interventions':
            loadInterventions();
            break;
        case 'spare-parts':
            loadSpareParts();
            break;
    }
}

function loadEquipments() {
    window.fastAPIRequest('/maintenance/equipments')
        .then(data => {
            const columns = [
                { field: 'code', title: 'Code' },
                { field: 'name', title: 'Nom' },
                { field: 'type', title: 'Type' },
                { field: 'location', title: 'Localisation' },
                { 
                    field: 'status', 
                    title: 'Statut',
                    render: (value) => window.industrialApp.getStatusBadge(value)
                },
                { 
                    field: 'last_maintenance', 
                    title: 'Dernière Maintenance',
                    render: (value) => value ? window.industrialApp.formatDate(value) : '-'
                },
                { 
                    field: 'next_maintenance', 
                    title: 'Prochaine Maintenance',
                    render: (value) => value ? window.industrialApp.formatDate(value) : '-'
                },
                {
                    field: 'id',
                    title: 'Actions',
                    render: (value, row) => `
                        <button class="btn btn-sm btn-secondary" onclick="editEquipment(${value})" title="Modifier">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="deleteEquipment(${value})" title="Supprimer">
                            <i class="fas fa-trash"></i>
                        </button>
                    `
                }
            ];
            
            window.industrialApp.createDataTable('equipments-table', data, columns);
        })
        .catch(error => {
            console.error('Erreur lors du chargement des équipements:', error);
            document.getElementById('equipments-table').innerHTML = '<div class="error">Erreur de chargement</div>';
        });
}

function loadInterventions() {
    window.fastAPIRequest('/maintenance/interventions')
        .then(data => {
            const columns = [
                { field: 'equipment', title: 'Équipement', render: (value) => value.name },
                { field: 'type', title: 'Type' },
                { field: 'description', title: 'Description' },
                { field: 'technician', title: 'Technicien' },
                { field: 'cost', title: 'Coût (€)', render: (value) => window.industrialApp.formatNumber(value, 2) },
                { field: 'status', title: 'Statut', render: (value) => window.industrialApp.getStatusBadge(value) }
            ];
            
            window.industrialApp.createDataTable('interventions-table', data, columns);
        })
        .catch(error => {
            console.error('Erreur lors du chargement des interventions:', error);
            document.getElementById('interventions-table').innerHTML = '<div class="error">Erreur de chargement</div>';
        });
}

function loadSpareParts() {
    window.fastAPIRequest('/maintenance/spare-parts')
        .then(data => {
            const columns = [
                { field: 'reference', title: 'Référence' },
                { field: 'name', title: 'Nom' },
                { field: 'equipment', title: 'Équipement', render: (value) => value.name },
                { field: 'quantity_stock', title: 'Stock' },
                { field: 'quantity_min', title: 'Stock Min' },
                { field: 'unit_price', title: 'Prix (€)', render: (value) => window.industrialApp.formatNumber(value, 2) },
                { field: 'supplier', title: 'Fournisseur' }
            ];
            
            window.industrialApp.createDataTable('spare-parts-table', data, columns);
        })
        .catch(error => {
            console.error('Erreur lors du chargement des pièces:', error);
            document.getElementById('spare-parts-table').innerHTML = '<div class="error">Erreur de chargement</div>';
        });
}

// Fonctions pour les modales
function openEquipmentModal() {
    document.getElementById('equipmentModal').classList.add('active');
}

function closeEquipmentModal() {
    document.getElementById('equipmentModal').classList.remove('active');
}

function editEquipment(equipmentId) {
    // TODO: Implémenter l'édition
    window.industrialApp.showNotification('Fonction d\'édition en cours de développement', 'info');
}

function deleteEquipment(equipmentId) {
    if (confirm('Êtes-vous sûr de vouloir supprimer cet équipement ?')) {
        window.fastAPIRequest(`/maintenance/equipments/${equipmentId}`, {
            method: 'DELETE'
        })
        .then(data => {
            window.industrialApp.showNotification('Équipement supprimé avec succès', 'success');
            loadEquipments();
        })
        .catch(error => {
            console.error('Erreur:', error);
            window.industrialApp.showNotification('Erreur lors de la suppression', 'error');
        });
    }
}

// Gestion du formulaire d'équipement
document.getElementById('equipmentForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const data = Object.fromEntries(formData.entries());
    
    window.fastAPIRequest('/maintenance/equipments', {
        method: 'POST',
        body: JSON.stringify(data)
    })
    .then(result => {
        window.industrialApp.showNotification('Équipement créé avec succès', 'success');
        closeEquipmentModal();
        loadEquipments();
        this.reset();
    })
    .catch(error => {
        console.error('Erreur:', error);
        window.industrialApp.showNotification('Erreur lors de la création: ' + error.message, 'error');
    });
});
</script>
{% endblock %}
