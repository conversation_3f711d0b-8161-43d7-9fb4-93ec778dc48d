"""
Configuration de la base de données pour FastAPI
"""

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool
from fastapi_models import Base
import os
from pathlib import Path

# Configuration de la base de données
BASE_DIR = Path(__file__).parent.absolute()
DATABASE_URL = f"sqlite:///{BASE_DIR / 'gestion_industrielle.db'}"

# Créer le moteur SQLAlchemy
engine = create_engine(
    DATABASE_URL,
    connect_args={
        "check_same_thread": False,  # Nécessaire pour SQLite
    },
    poolclass=StaticPool,
    echo=False  # Mettre à True pour voir les requêtes SQL
)

# Créer la session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def create_tables():
    """Créer toutes les tables"""
    Base.metadata.create_all(bind=engine)
    print("✅ Tables créées avec succès!")

def get_db() -> Session:
    """
    Dépendance pour obtenir une session de base de données
    Utilisée avec FastAPI Depends()
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def init_database():
    """Initialiser la base de données"""
    try:
        create_tables()
        return True
    except Exception as e:
        print(f"❌ Erreur lors de l'initialisation de la base de données: {e}")
        return False

def reset_database():
    """Réinitialiser complètement la base de données"""
    try:
        Base.metadata.drop_all(bind=engine)
        Base.metadata.create_all(bind=engine)
        print("✅ Base de données réinitialisée avec succès!")
        return True
    except Exception as e:
        print(f"❌ Erreur lors de la réinitialisation: {e}")
        return False
